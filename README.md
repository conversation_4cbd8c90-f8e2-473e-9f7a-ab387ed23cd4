# Design Patterns في C# - دليل شامل 🎯

## مقدمة
هذا المشروع يحتوي على شرح وتطبيق عملي لأهم Design Patterns في البرمجة باستخدام لغة C#. تم تصميم المشروع ليكون دليلاً تعليمياً شاملاً مع أمثلة عملية وتطبيقية.

## الأنماط المشمولة

### 🏗️ 1. Creational Patterns (أنماط الإنشاء)
- **Prototype Pattern**: لإنشاء كائنات جديدة عن طريق نسخ كائنات موجودة
  - مثال: نظام إدارة الموظفين مع نماذج أولية

### 🎭 2. Behavioral Patterns (الأنماط السلوكية)
- **Strategy Pattern**: لتغيير سلوك الكائن في وقت التشغيل
  - مثال: نظام دفع متعدد الطرق (بطاقة ائتمانية، PayPal، تحويل بنكي)
- **State Pattern**: لتغيير سلوك الكائن بناءً على حالته الداخلية
  - مثال: دورة حياة الطلب (معلق، مؤكد، مشحون، مسلم، ملغي، مرجع)

### 🏗️ 3. Structural Patterns (الأنماط الهيكلية)
- **Facade Pattern**: لتبسيط واجهة معقدة
  - مثال: نظام المسرح المنزلي الذكي
- **Adapter Pattern**: لجعل واجهات غير متوافقة تعمل معاً
  - مثال: توحيد أنظمة دفع مختلفة (البنك الأهلي، بنك الرياض، البوابة الدولية)
- **Decorator Pattern**: لإضافة وظائف جديدة للكائنات دون تعديل هيكلها
  - مثال: نظام طلب القهوة مع إضافات متعددة
- **Proxy Pattern**: للتحكم في الوصول إلى كائن آخر
  - مثال: نظام إدارة المستندات مع صلاحيات وتخزين مؤقت

## هيكل المشروع

```
Design Pattern/
├── README.md
├── DesignPatterns.csproj
├── CreationalPatterns/
│   └── Prototype/
│       ├── IPrototype.cs
│       ├── Employee.cs
│       ├── Address.cs
│       ├── EmployeeRegistry.cs
│       └── PrototypeDemo.cs
├── BehavioralPatterns/
│   ├── Strategy/
│   │   ├── IPaymentStrategy.cs
│   │   ├── CreditCardStrategy.cs
│   │   ├── PayPalStrategy.cs
│   │   ├── BankTransferStrategy.cs
│   │   ├── PaymentProcessor.cs
│   │   └── StrategyDemo.cs
│   └── State/
│       ├── IOrderState.cs
│       ├── OrderContext.cs
│       ├── PendingState.cs
│       ├── ConfirmedState.cs
│       ├── ShippedState.cs
│       ├── DeliveredState.cs
│       ├── CancelledState.cs
│       ├── ReturnedState.cs
│       └── StateDemo.cs
├── StructuralPatterns/
│   ├── Facade/
│   │   ├── HomeTheaterSubsystems.cs
│   │   ├── HomeTheaterFacade.cs
│   │   └── FacadeDemo.cs
│   ├── Adapter/
│   │   ├── IPaymentProcessor.cs
│   │   ├── LegacyPaymentSystems.cs
│   │   ├── PaymentAdapters.cs
│   │   ├── PaymentService.cs
│   │   └── AdapterDemo.cs
│   ├── Decorator/
│   │   ├── ICoffee.cs
│   │   ├── BasicCoffees.cs
│   │   ├── CoffeeDecorator.cs
│   │   ├── CoffeeAddons.cs
│   │   ├── CoffeeShop.cs
│   │   └── DecoratorDemo.cs
│   └── Proxy/
│       ├── IDocumentService.cs
│       ├── RealDocumentService.cs
│       ├── DocumentServiceProxy.cs
│       └── ProxyDemo.cs
└── Examples/
    └── Program.cs
```

## كيفية تشغيل الأمثلة

### الطريقة الأولى: تشغيل البرنامج التفاعلي
```bash
# 1. انتقل إلى مجلد المشروع
cd "Design Pattern"

# 2. تشغيل البرنامج
dotnet run
```

### الطريقة الثانية: تشغيل نمط محدد
```bash
# تشغيل نمط معين مباشرة (مثال)
dotnet run --project . 1  # لتشغيل Prototype Pattern
```

### الطريقة الثالثة: استخدام IDE
1. افتح المشروع في Visual Studio أو VS Code
2. اضبط `Examples/Program.cs` كملف البداية
3. اضغط F5 أو Run

## الفوائد من استخدام Design Patterns

### 🎯 الفوائد التقنية
1. **إعادة الاستخدام**: حلول مجربة ومختبرة
2. **المرونة**: سهولة التعديل والتوسع
3. **الجودة**: كود أكثر تنظيماً وقابلية للصيانة
4. **الأداء**: تحسينات في الذاكرة والسرعة

### 🤝 الفوائد التعاونية
1. **التواصل**: لغة مشتركة بين المطورين
2. **التوثيق**: الكود يوثق نفسه بنفسه
3. **التدريب**: سهولة تعليم المطورين الجدد
4. **المراجعة**: سهولة مراجعة وفهم الكود

### 📈 الفوائد التجارية
1. **التطوير السريع**: تقليل وقت التطوير
2. **تقليل الأخطاء**: حلول مختبرة ومجربة
3. **سهولة الصيانة**: تقليل تكلفة الصيانة
4. **القابلية للتوسع**: سهولة إضافة ميزات جديدة

## متطلبات النظام

- **.NET 6.0** أو أحدث
- **Visual Studio 2022** أو **Visual Studio Code** (اختياري)
- **Git** (لتحميل المشروع)

## الميزات المتقدمة

### 🔧 ميزات البرنامج التفاعلي
- قائمة تفاعلية لاختيار النمط
- عرض مفصل لكل نمط مع أمثلة
- إمكانية تشغيل جميع الأمثلة دفعة واحدة
- ملخص شامل لجميع الأنماط

### 📊 ميزات تعليمية
- شرح مفصل لكل نمط
- أمثلة عملية من الحياة الواقعية
- مقارنات بين الحلول مع وبدون الأنماط
- نصائح وأفضل الممارسات

### 🎨 ميزات تقنية
- كود منظم ومعلق باللغة العربية
- اتباع مبادئ SOLID
- استخدام أحدث ميزات C#
- معالجة شاملة للأخطاء

## أمثلة الاستخدام

### مثال سريع - Prototype Pattern
```csharp
// إنشاء سجل الموظفين
var employeeRegistry = new EmployeeRegistry();

// إنشاء موظف جديد من نموذج أولي
var developer = employeeRegistry.CreateEmployee("Developer", "أحمد محمد");
developer.Salary = 9000;
developer.AddSkill("React");

Console.WriteLine(developer.ToString());
```

### مثال سريع - Strategy Pattern
```csharp
// إنشاء معالج الدفعات
var paymentProcessor = new PaymentProcessor();

// تعيين استراتيجية الدفع
paymentProcessor.SetPaymentStrategy(
    new CreditCardStrategy("1234567890123456", "أحمد محمد", "12/25", "123")
);

// معالجة الدفع
bool success = paymentProcessor.ProcessPayment(250.75m);
```

## المساهمة في المشروع

نرحب بمساهماتكم! يمكنكم:
1. إضافة أنماط تصميم جديدة
2. تحسين الأمثلة الموجودة
3. إضافة ترجمات لغات أخرى
4. تحسين التوثيق

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

---

*تم إنشاء هذا المشروع لأغراض تعليمية لفهم وتطبيق Design Patterns في C#*

**المطور**: مساعد الذكي الاصطناعي
**التاريخ**: 2024
**الإصدار**: 1.0
