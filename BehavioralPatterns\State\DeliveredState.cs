using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// حالة "تم التسليم"
    /// الطلب وصل للعميل بنجاح
    /// </summary>
    public class DeliveredState : IOrderState
    {
        public void ConfirmOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب مؤكد ومسلم بالفعل");
        }

        public void ShipOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب مشحون ومسلم بالفعل");
        }

        public void DeliverOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب مسلم بالفعل");
        }

        public void CancelOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن إلغاء طلب تم تسليمه");
            Console.WriteLine("💡 يمكنك استخدام خيار الإرجاع بدلاً من ذلك");
        }

        public void ReturnOrder(OrderContext context)
        {
            Console.WriteLine("📦 تم بدء عملية إرجاع الطلب");
            Console.WriteLine("📋 يرجى تعبئة نموذج الإرجاع");
            Console.WriteLine("🚚 سيتم إرسال مندوب لاستلام الطلب");
            Console.WriteLine("💰 سيتم استرداد المبلغ بعد فحص المنتج");
            context.SetState(new ReturnedState());
        }

        public string GetStateName()
        {
            return "تم التسليم";
        }

        public string[] GetAvailableActions()
        {
            return new[] { "إرجاع الطلب" };
        }
    }
}
