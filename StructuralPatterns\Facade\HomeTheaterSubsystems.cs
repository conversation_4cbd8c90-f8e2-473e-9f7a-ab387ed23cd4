using System;
using System.Threading;

namespace DesignPatterns.StructuralPatterns.Facade
{
    /// <summary>
    /// الأنظمة الفرعية المعقدة لنظام المسرح المنزلي
    /// هذه الكلاسات تمثل مكونات مختلفة معقدة
    /// </summary>

    // نظام الصوت
    public class AudioSystem
    {
        public void TurnOn()
        {
            Console.WriteLine("🔊 تشغيل نظام الصوت...");
            Thread.Sleep(500);
        }

        public void TurnOff()
        {
            Console.WriteLine("🔇 إيقاف نظام الصوت");
        }

        public void SetVolume(int volume)
        {
            Console.WriteLine($"🔉 ضبط مستوى الصوت إلى {volume}%");
        }

        public void SetSurroundSound()
        {
            Console.WriteLine("🎵 تفعيل الصوت المحيطي 5.1");
        }
    }

    // نظام العرض (البروجيكتر)
    public class ProjectorSystem
    {
        public void TurnOn()
        {
            Console.WriteLine("📽️ تشغيل البروجيكتر...");
            Thread.Sleep(800);
        }

        public void TurnOff()
        {
            Console.WriteLine("📴 إيقاف البروجيكتر");
        }

        public void SetInput(string input)
        {
            Console.WriteLine($"📺 ضبط مدخل البروجيكتر إلى {input}");
        }

        public void SetResolution(string resolution)
        {
            Console.WriteLine($"🖥️ ضبط الدقة إلى {resolution}");
        }
    }

    // نظام الإضاءة
    public class LightingSystem
    {
        public void TurnOff()
        {
            Console.WriteLine("💡 إطفاء الأضواء");
        }

        public void TurnOn()
        {
            Console.WriteLine("🔆 تشغيل الأضواء");
        }

        public void SetDimLevel(int level)
        {
            Console.WriteLine($"🌅 ضبط خفوت الإضاءة إلى {level}%");
        }

        public void SetMovieMode()
        {
            Console.WriteLine("🎬 تفعيل وضع الإضاءة السينمائية");
        }
    }

    // مشغل الأقراص
    public class DVDPlayer
    {
        public void TurnOn()
        {
            Console.WriteLine("💿 تشغيل مشغل الأقراص...");
            Thread.Sleep(300);
        }

        public void TurnOff()
        {
            Console.WriteLine("⏹️ إيقاف مشغل الأقراص");
        }

        public void InsertDisc(string movieTitle)
        {
            Console.WriteLine($"📀 إدخال قرص: {movieTitle}");
        }

        public void Play()
        {
            Console.WriteLine("▶️ بدء تشغيل الفيلم");
        }

        public void Stop()
        {
            Console.WriteLine("⏹️ إيقاف تشغيل الفيلم");
        }

        public void Eject()
        {
            Console.WriteLine("⏏️ إخراج القرص");
        }
    }

    // نظام التكييف
    public class ClimateControl
    {
        public void SetTemperature(int temperature)
        {
            Console.WriteLine($"🌡️ ضبط درجة الحرارة إلى {temperature}°C");
        }

        public void SetMovieMode()
        {
            Console.WriteLine("❄️ تفعيل وضع التكييف المريح للأفلام");
        }

        public void TurnOff()
        {
            Console.WriteLine("🔌 إيقاف نظام التكييف");
        }
    }

    // نظام الستائر الآلية
    public class CurtainSystem
    {
        public void Close()
        {
            Console.WriteLine("🪟 إغلاق الستائر...");
            Thread.Sleep(600);
        }

        public void Open()
        {
            Console.WriteLine("🌞 فتح الستائر");
        }
    }
}
