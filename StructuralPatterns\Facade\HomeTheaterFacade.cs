using System;
using System.Threading;

namespace DesignPatterns.StructuralPatterns.Facade
{
    /// <summary>
    /// واجهة المسرح المنزلي - Facade Pattern
    /// توفر واجهة مبسطة للتحكم في جميع أنظمة المسرح المنزلي المعقدة
    /// </summary>
    public class HomeTheaterFacade
    {
        // الأنظمة الفرعية المعقدة
        private readonly AudioSystem _audioSystem;
        private readonly ProjectorSystem _projectorSystem;
        private readonly LightingSystem _lightingSystem;
        private readonly DVDPlayer _dvdPlayer;
        private readonly ClimateControl _climateControl;
        private readonly CurtainSystem _curtainSystem;

        public HomeTheaterFacade()
        {
            // تهيئة جميع الأنظمة الفرعية
            _audioSystem = new AudioSystem();
            _projectorSystem = new ProjectorSystem();
            _lightingSystem = new LightingSystem();
            _dvdPlayer = new DVDPlayer();
            _climateControl = new ClimateControl();
            _curtainSystem = new CurtainSystem();
        }

        /// <summary>
        /// بدء مشاهدة فيلم - عملية معقدة مبسطة في دالة واحدة
        /// </summary>
        /// <param name="movieTitle">اسم الفيلم</param>
        public void WatchMovie(string movieTitle)
        {
            Console.WriteLine($"🎬 بدء تحضير المسرح المنزلي لمشاهدة: {movieTitle}");
            Console.WriteLine(new string('=', 60));

            // تسلسل العمليات المعقدة
            _curtainSystem.Close();
            _lightingSystem.SetMovieMode();
            _climateControl.SetMovieMode();
            
            _projectorSystem.TurnOn();
            _projectorSystem.SetInput("DVD");
            _projectorSystem.SetResolution("4K");
            
            _audioSystem.TurnOn();
            _audioSystem.SetSurroundSound();
            _audioSystem.SetVolume(75);
            
            _dvdPlayer.TurnOn();
            _dvdPlayer.InsertDisc(movieTitle);
            
            Console.WriteLine("✨ المسرح المنزلي جاهز!");
            Thread.Sleep(1000);
            
            _dvdPlayer.Play();
            Console.WriteLine($"🍿 استمتع بمشاهدة {movieTitle}!");
        }

        /// <summary>
        /// إنهاء مشاهدة الفيلم - إيقاف جميع الأنظمة
        /// </summary>
        public void EndMovie()
        {
            Console.WriteLine("\n🔚 إنهاء مشاهدة الفيلم...");
            Console.WriteLine(new string('=', 40));

            _dvdPlayer.Stop();
            _dvdPlayer.Eject();
            _dvdPlayer.TurnOff();
            
            _projectorSystem.TurnOff();
            _audioSystem.TurnOff();
            
            _lightingSystem.TurnOn();
            _curtainSystem.Open();
            _climateControl.TurnOff();
            
            Console.WriteLine("✅ تم إيقاف جميع أنظمة المسرح المنزلي");
        }

        /// <summary>
        /// وضع الاستماع للموسيقى
        /// </summary>
        /// <param name="playlist">قائمة التشغيل</param>
        public void ListenToMusic(string playlist)
        {
            Console.WriteLine($"🎵 تحضير النظام للاستماع إلى: {playlist}");
            Console.WriteLine(new string('=', 50));

            _lightingSystem.SetDimLevel(30);
            _climateControl.SetTemperature(22);
            
            _audioSystem.TurnOn();
            _audioSystem.SetSurroundSound();
            _audioSystem.SetVolume(60);
            
            Console.WriteLine($"🎶 جاري تشغيل {playlist}");
        }

        /// <summary>
        /// وضع الألعاب
        /// </summary>
        /// <param name="gameName">اسم اللعبة</param>
        public void PlayGame(string gameName)
        {
            Console.WriteLine($"🎮 تحضير النظام للعب: {gameName}");
            Console.WriteLine(new string('=', 45));

            _lightingSystem.SetDimLevel(50);
            _climateControl.SetTemperature(20);
            
            _projectorSystem.TurnOn();
            _projectorSystem.SetInput("Gaming Console");
            _projectorSystem.SetResolution("4K 120Hz");
            
            _audioSystem.TurnOn();
            _audioSystem.SetVolume(80);
            
            Console.WriteLine($"🕹️ جاهز للعب {gameName}!");
        }

        /// <summary>
        /// إيقاف جميع الأنظمة
        /// </summary>
        public void ShutdownAll()
        {
            Console.WriteLine("\n🔌 إيقاف جميع أنظمة المسرح المنزلي...");
            Console.WriteLine(new string('=', 45));

            _dvdPlayer.TurnOff();
            _projectorSystem.TurnOff();
            _audioSystem.TurnOff();
            _lightingSystem.TurnOn();
            _curtainSystem.Open();
            _climateControl.TurnOff();
            
            Console.WriteLine("💤 تم إيقاف جميع الأنظمة - وضع الاستعداد");
        }

        /// <summary>
        /// عرض حالة جميع الأنظمة
        /// </summary>
        public void ShowSystemStatus()
        {
            Console.WriteLine("\n📊 حالة أنظمة المسرح المنزلي:");
            Console.WriteLine(new string('-', 35));
            Console.WriteLine("🔊 نظام الصوت: متصل");
            Console.WriteLine("📽️ البروجيكتر: متصل");
            Console.WriteLine("💡 نظام الإضاءة: متصل");
            Console.WriteLine("💿 مشغل الأقراص: متصل");
            Console.WriteLine("🌡️ نظام التكييف: متصل");
            Console.WriteLine("🪟 نظام الستائر: متصل");
            Console.WriteLine("✅ جميع الأنظمة تعمل بشكل طبيعي");
        }
    }
}
