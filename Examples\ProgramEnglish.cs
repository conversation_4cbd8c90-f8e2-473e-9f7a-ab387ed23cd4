using System;
using DesignPatterns.CreationalPatterns.Prototype;
using DesignPatterns.BehavioralPatterns.Strategy;
using DesignPatterns.BehavioralPatterns.State;
using DesignPatterns.StructuralPatterns.Facade;
using DesignPatterns.StructuralPatterns.Adapter;
using DesignPatterns.StructuralPatterns.Decorator;
using DesignPatterns.StructuralPatterns.Proxy;

namespace DesignPatterns.Examples
{
    /// <summary>
    /// English Version - Main Program - Comprehensive Demo of All Design Patterns
    /// </summary>
    class ProgramEnglish
    {
        static void MainEnglish(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
            
            try
            {
                Console.Title = "Design Patterns Tutorial";
            }
            catch { }
            
            ShowWelcomeMessage();
            
            bool continueRunning = true;
            
            while (continueRunning)
            {
                ShowMainMenu();
                var choice = Console.ReadLine();
                
                try
                {
                    switch (choice)
                    {
                        case "1":
                            RunPrototypeDemo();
                            break;
                        case "2":
                            RunStrategyDemo();
                            break;
                        case "3":
                            RunStateDemo();
                            break;
                        case "4":
                            RunFacadeDemo();
                            break;
                        case "5":
                            RunAdapterDemo();
                            break;
                        case "6":
                            RunDecoratorDemo();
                            break;
                        case "7":
                            RunProxyDemo();
                            break;
                        case "8":
                            RunAllDemos();
                            break;
                        case "9":
                            ShowPatternsSummary();
                            break;
                        case "0":
                            continueRunning = false;
                            Console.WriteLine("Thank you for using Design Patterns Tutorial! 👋");
                            break;
                        default:
                            Console.WriteLine("❌ Invalid choice, please try again");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Error occurred: {ex.Message}");
                }
                
                if (continueRunning)
                {
                    Console.WriteLine("\nPress any key to continue...");
                    Console.ReadKey();
                    Console.Clear();
                }
            }
        }

        static void ShowWelcomeMessage()
        {
            Console.Clear();
            Console.WriteLine(new string('=', 80));
            Console.WriteLine("🎯 Welcome to Design Patterns Tutorial");
            Console.WriteLine("📚 Comprehensive Guide to Important Programming Design Patterns");
            Console.WriteLine("💻 Developed in C# with Practical Examples");
            Console.WriteLine(new string('=', 80));
            Console.WriteLine();
        }

        static void ShowMainMenu()
        {
            Console.WriteLine("📋 Main Menu - Choose the pattern you want to learn:");
            Console.WriteLine(new string('-', 60));
            Console.WriteLine();
            
            Console.WriteLine("🏗️  Creational Patterns:");
            Console.WriteLine("   1️⃣  Prototype Pattern");
            Console.WriteLine();
            
            Console.WriteLine("🎭 Behavioral Patterns:");
            Console.WriteLine("   2️⃣  Strategy Pattern");
            Console.WriteLine("   3️⃣  State Pattern");
            Console.WriteLine();
            
            Console.WriteLine("🏗️  Structural Patterns:");
            Console.WriteLine("   4️⃣  Facade Pattern");
            Console.WriteLine("   5️⃣  Adapter Pattern");
            Console.WriteLine("   6️⃣  Decorator Pattern");
            Console.WriteLine("   7️⃣  Proxy Pattern");
            Console.WriteLine();
            
            Console.WriteLine("🎪 Additional Options:");
            Console.WriteLine("   8️⃣  Run All Examples");
            Console.WriteLine("   9️⃣  Patterns Summary");
            Console.WriteLine("   0️⃣  Exit");
            Console.WriteLine();
            
            Console.Write("👉 Choose pattern number (0-9): ");
        }

        static void RunPrototypeDemo()
        {
            Console.Clear();
            Console.WriteLine("🏗️ Running Prototype Pattern Demo");
            Console.WriteLine(new string('=', 50));
            PrototypeDemo.RunDemo();
        }

        static void RunStrategyDemo()
        {
            Console.Clear();
            Console.WriteLine("🎯 Running Strategy Pattern Demo");
            Console.WriteLine(new string('=', 50));
            StrategyDemo.RunDemo();
        }

        static void RunStateDemo()
        {
            Console.Clear();
            Console.WriteLine("🔄 Running State Pattern Demo");
            Console.WriteLine(new string('=', 50));
            StateDemo.RunDemo();
        }

        static void RunFacadeDemo()
        {
            Console.Clear();
            Console.WriteLine("🎭 Running Facade Pattern Demo");
            Console.WriteLine(new string('=', 50));
            FacadeDemo.RunDemo();
        }

        static void RunAdapterDemo()
        {
            Console.Clear();
            Console.WriteLine("🔌 Running Adapter Pattern Demo");
            Console.WriteLine(new string('=', 50));
            AdapterDemo.RunDemo();
        }

        static void RunDecoratorDemo()
        {
            Console.Clear();
            Console.WriteLine("☕ Running Decorator Pattern Demo");
            Console.WriteLine(new string('=', 50));
            DecoratorDemo.RunDemo();
        }

        static void RunProxyDemo()
        {
            Console.Clear();
            Console.WriteLine("🔐 Running Proxy Pattern Demo");
            Console.WriteLine(new string('=', 50));
            ProxyDemo.RunDemo();
        }

        static void RunAllDemos()
        {
            Console.Clear();
            Console.WriteLine("🎪 Running All Examples");
            Console.WriteLine(new string('=', 50));
            
            var patterns = new[]
            {
                ("Prototype Pattern", (Action)(() => PrototypeDemo.RunDemo())),
                ("Strategy Pattern", (Action)(() => StrategyDemo.RunDemo())),
                ("State Pattern", (Action)(() => StateDemo.RunDemo())),
                ("Facade Pattern", (Action)(() => FacadeDemo.RunDemo())),
                ("Adapter Pattern", (Action)(() => AdapterDemo.RunDemo())),
                ("Decorator Pattern", (Action)(() => DecoratorDemo.RunDemo())),
                ("Proxy Pattern", (Action)(() => ProxyDemo.RunDemo()))
            };

            for (int i = 0; i < patterns.Length; i++)
            {
                Console.WriteLine($"\n🎯 Running {patterns[i].Item1} ({i + 1}/{patterns.Length})");
                Console.WriteLine(new string('*', 60));
                
                patterns[i].Item2();
                
                if (i < patterns.Length - 1)
                {
                    Console.WriteLine("\n" + new string('=', 80));
                    Console.WriteLine("Press any key to continue to next pattern...");
                    Console.ReadKey();
                    Console.WriteLine();
                }
            }
            
            Console.WriteLine("\n🎉 All examples completed successfully!");
        }

        static void ShowPatternsSummary()
        {
            Console.Clear();
            Console.WriteLine("📚 Design Patterns Summary");
            Console.WriteLine(new string('=', 60));
            
            Console.WriteLine("\n🏗️ Creational Patterns:");
            Console.WriteLine("   📋 Prototype Pattern:");
            Console.WriteLine("      • Purpose: Create new objects by copying existing ones");
            Console.WriteLine("      • Use: When object creation is expensive or complex");
            Console.WriteLine("      • Example: Employee management system");
            
            Console.WriteLine("\n🎭 Behavioral Patterns:");
            Console.WriteLine("   🎯 Strategy Pattern:");
            Console.WriteLine("      • Purpose: Change object behavior at runtime");
            Console.WriteLine("      • Use: When you want different algorithms for same task");
            Console.WriteLine("      • Example: Multiple payment methods");
            
            Console.WriteLine("   🔄 State Pattern:");
            Console.WriteLine("      • Purpose: Change object behavior based on internal state");
            Console.WriteLine("      • Use: When object has multiple complex states");
            Console.WriteLine("      • Example: Order lifecycle management");
            
            Console.WriteLine("\n🏗️ Structural Patterns:");
            Console.WriteLine("   🎭 Facade Pattern:");
            Console.WriteLine("      • Purpose: Simplify complex interface");
            Console.WriteLine("      • Use: When you want to hide system complexity");
            Console.WriteLine("      • Example: Home theater system");
            
            Console.WriteLine("   🔌 Adapter Pattern:");
            Console.WriteLine("      • Purpose: Make incompatible interfaces work together");
            Console.WriteLine("      • Use: When integrating legacy systems");
            Console.WriteLine("      • Example: Unifying different payment systems");
            
            Console.WriteLine("   ☕ Decorator Pattern:");
            Console.WriteLine("      • Purpose: Add new functionality without modifying structure");
            Console.WriteLine("      • Use: When you want flexible feature combinations");
            Console.WriteLine("      • Example: Coffee ordering system");
            
            Console.WriteLine("   🔐 Proxy Pattern:");
            Console.WriteLine("      • Purpose: Control access to another object");
            Console.WriteLine("      • Use: For security, caching, or lazy loading");
            Console.WriteLine("      • Example: Document management system");
            
            Console.WriteLine("\n🎯 General Benefits of Design Patterns:");
            Console.WriteLine("   ✅ Proven and tested solutions");
            Console.WriteLine("   ✅ Common language among developers");
            Console.WriteLine("   ✅ Improved code quality and maintainability");
            Console.WriteLine("   ✅ Easier reuse and extension");
            Console.WriteLine("   ✅ Following good programming principles (SOLID)");
        }
    }
}
