namespace DesignPatterns.StructuralPatterns.Decorator
{
    /// <summary>
    /// الكلاس الأساسي للمزخرفات - Base Decorator
    /// يحتوي على مرجع للكائن الأساسي ويفوض العمليات إليه
    /// </summary>
    public abstract class CoffeeDecorator : ICoffee
    {
        protected ICoffee _coffee;

        protected CoffeeDecorator(ICoffee coffee)
        {
            _coffee = coffee ?? throw new System.ArgumentNullException(nameof(coffee));
        }

        // تفويض العمليات الأساسية للكائن المُزخرف
        public virtual string GetDescription()
        {
            return _coffee.GetDescription();
        }

        public virtual decimal GetCost()
        {
            return _coffee.GetCost();
        }

        public virtual NutritionInfo GetNutritionInfo()
        {
            return _coffee.GetNutritionInfo();
        }
    }
}
