using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// حالة "تم الشحن"
    /// الطلب في الطريق للعميل
    /// </summary>
    public class ShippedState : IOrderState
    {
        public void ConfirmOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب مؤكد ومشحون بالفعل");
        }

        public void ShipOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب مشحون بالفعل");
        }

        public void DeliverOrder(OrderContext context)
        {
            Console.WriteLine("🎉 تم تسليم الطلب بنجاح!");
            Console.WriteLine("📝 تم الحصول على توقيع العميل");
            Console.WriteLine("⭐ يرجى تقييم الخدمة");
            context.SetState(new DeliveredState());
        }

        public void CancelOrder(OrderContext context)
        {
            Console.WriteLine("⚠️ الطلب في الطريق - سيتم محاولة إيقاف الشحنة");
            Console.WriteLine("📞 سيتم التواصل معك خلال ساعة");
            Console.WriteLine("💰 إذا لم يتم إيقاف الشحنة، يمكنك رفض الاستلام");
            // في الواقع، قد نحتاج حالة خاصة "محاولة الإلغاء"
            Console.WriteLine("🔄 تم تحويل الطلب إلى حالة الإلغاء");
            context.SetState(new CancelledState());
        }

        public void ReturnOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن إرجاع طلب لم يتم تسليمه بعد");
            Console.WriteLine("💡 يمكنك رفض الاستلام عند وصول الطلب");
        }

        public string GetStateName()
        {
            return "تم الشحن";
        }

        public string[] GetAvailableActions()
        {
            return new[] { "تسليم الطلب", "إلغاء الطلب (طارئ)" };
        }
    }
}
