using System;
using DesignPatterns.CreationalPatterns.Prototype;
using DesignPatterns.BehavioralPatterns.Strategy;
using DesignPatterns.BehavioralPatterns.State;
using DesignPatterns.StructuralPatterns.Facade;
using DesignPatterns.StructuralPatterns.Adapter;
using DesignPatterns.StructuralPatterns.Decorator;
using DesignPatterns.StructuralPatterns.Proxy;

namespace DesignPatterns.Examples
{
    /// <summary>
    /// البرنامج الرئيسي - عرض شامل لجميع Design Patterns
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            
            ShowWelcomeMessage();
            
            bool continueRunning = true;
            
            while (continueRunning)
            {
                ShowMainMenu();
                var choice = Console.ReadLine();
                
                try
                {
                    switch (choice)
                    {
                        case "1":
                            RunPrototypeDemo();
                            break;
                        case "2":
                            RunStrategyDemo();
                            break;
                        case "3":
                            RunStateDemo();
                            break;
                        case "4":
                            RunFacadeDemo();
                            break;
                        case "5":
                            RunAdapterDemo();
                            break;
                        case "6":
                            RunDecoratorDemo();
                            break;
                        case "7":
                            RunProxyDemo();
                            break;
                        case "8":
                            RunAllDemos();
                            break;
                        case "9":
                            ShowPatternsSummary();
                            break;
                        case "0":
                            continueRunning = false;
                            Console.WriteLine("شكراً لاستخدام برنامج Design Patterns! 👋");
                            break;
                        default:
                            Console.WriteLine("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ حدث خطأ: {ex.Message}");
                }
                
                if (continueRunning)
                {
                    Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                    Console.ReadKey();
                    Console.Clear();
                }
            }
        }

        static void ShowWelcomeMessage()
        {
            Console.Clear();
            Console.WriteLine(new string('=', 80));
            Console.WriteLine("🎯 مرحباً بك في برنامج Design Patterns التعليمي");
            Console.WriteLine("📚 دليل شامل لأهم أنماط التصميم في البرمجة");
            Console.WriteLine("💻 مطور بلغة C# مع أمثلة عملية وتطبيقية");
            Console.WriteLine(new string('=', 80));
            Console.WriteLine();
        }

        static void ShowMainMenu()
        {
            Console.WriteLine("📋 القائمة الرئيسية - اختر النمط الذي تريد تعلمه:");
            Console.WriteLine(new string('-', 60));
            Console.WriteLine();
            
            Console.WriteLine("🏗️  الأنماط الإنشائية (Creational Patterns):");
            Console.WriteLine("   1️⃣  Prototype Pattern - نمط النموذج الأولي");
            Console.WriteLine();
            
            Console.WriteLine("🎭 الأنماط السلوكية (Behavioral Patterns):");
            Console.WriteLine("   2️⃣  Strategy Pattern - نمط الاستراتيجية");
            Console.WriteLine("   3️⃣  State Pattern - نمط الحالة");
            Console.WriteLine();
            
            Console.WriteLine("🏗️  الأنماط الهيكلية (Structural Patterns):");
            Console.WriteLine("   4️⃣  Facade Pattern - نمط الواجهة");
            Console.WriteLine("   5️⃣  Adapter Pattern - نمط المحول");
            Console.WriteLine("   6️⃣  Decorator Pattern - نمط المزخرف");
            Console.WriteLine("   7️⃣  Proxy Pattern - نمط الوكيل");
            Console.WriteLine();
            
            Console.WriteLine("🎪 خيارات إضافية:");
            Console.WriteLine("   8️⃣  تشغيل جميع الأمثلة");
            Console.WriteLine("   9️⃣  ملخص جميع الأنماط");
            Console.WriteLine("   0️⃣  خروج");
            Console.WriteLine();
            
            Console.Write("👉 اختر رقم النمط (0-9): ");
        }

        static void RunPrototypeDemo()
        {
            Console.Clear();
            Console.WriteLine("🏗️ تشغيل عرض Prototype Pattern");
            Console.WriteLine(new string('=', 50));
            PrototypeDemo.RunDemo();
        }

        static void RunStrategyDemo()
        {
            Console.Clear();
            Console.WriteLine("🎯 تشغيل عرض Strategy Pattern");
            Console.WriteLine(new string('=', 50));
            StrategyDemo.RunDemo();
        }

        static void RunStateDemo()
        {
            Console.Clear();
            Console.WriteLine("🔄 تشغيل عرض State Pattern");
            Console.WriteLine(new string('=', 50));
            StateDemo.RunDemo();
        }

        static void RunFacadeDemo()
        {
            Console.Clear();
            Console.WriteLine("🎭 تشغيل عرض Facade Pattern");
            Console.WriteLine(new string('=', 50));
            FacadeDemo.RunDemo();
        }

        static void RunAdapterDemo()
        {
            Console.Clear();
            Console.WriteLine("🔌 تشغيل عرض Adapter Pattern");
            Console.WriteLine(new string('=', 50));
            AdapterDemo.RunDemo();
        }

        static void RunDecoratorDemo()
        {
            Console.Clear();
            Console.WriteLine("☕ تشغيل عرض Decorator Pattern");
            Console.WriteLine(new string('=', 50));
            DecoratorDemo.RunDemo();
        }

        static void RunProxyDemo()
        {
            Console.Clear();
            Console.WriteLine("🔐 تشغيل عرض Proxy Pattern");
            Console.WriteLine(new string('=', 50));
            ProxyDemo.RunDemo();
        }

        static void RunAllDemos()
        {
            Console.Clear();
            Console.WriteLine("🎪 تشغيل جميع الأمثلة");
            Console.WriteLine(new string('=', 50));
            
            var patterns = new[]
            {
                ("Prototype Pattern", (Action)(() => PrototypeDemo.RunDemo())),
                ("Strategy Pattern", (Action)(() => StrategyDemo.RunDemo())),
                ("State Pattern", (Action)(() => StateDemo.RunDemo())),
                ("Facade Pattern", (Action)(() => FacadeDemo.RunDemo())),
                ("Adapter Pattern", (Action)(() => AdapterDemo.RunDemo())),
                ("Decorator Pattern", (Action)(() => DecoratorDemo.RunDemo())),
                ("Proxy Pattern", (Action)(() => ProxyDemo.RunDemo()))
            };

            for (int i = 0; i < patterns.Length; i++)
            {
                Console.WriteLine($"\n🎯 تشغيل {patterns[i].Item1} ({i + 1}/{patterns.Length})");
                Console.WriteLine(new string('*', 60));
                
                patterns[i].Item2();
                
                if (i < patterns.Length - 1)
                {
                    Console.WriteLine("\n" + new string('=', 80));
                    Console.WriteLine("اضغط أي مفتاح للانتقال للنمط التالي...");
                    Console.ReadKey();
                    Console.WriteLine();
                }
            }
            
            Console.WriteLine("\n🎉 تم تشغيل جميع الأمثلة بنجاح!");
        }

        static void ShowPatternsSummary()
        {
            Console.Clear();
            Console.WriteLine("📚 ملخص جميع Design Patterns");
            Console.WriteLine(new string('=', 60));
            
            Console.WriteLine("\n🏗️ الأنماط الإنشائية (Creational Patterns):");
            Console.WriteLine("   📋 Prototype Pattern:");
            Console.WriteLine("      • الغرض: إنشاء كائنات جديدة عن طريق نسخ كائنات موجودة");
            Console.WriteLine("      • الاستخدام: عندما يكون إنشاء الكائن مكلفاً أو معقداً");
            Console.WriteLine("      • المثال: نسخ ملفات تعريف الموظفين");
            
            Console.WriteLine("\n🎭 الأنماط السلوكية (Behavioral Patterns):");
            Console.WriteLine("   🎯 Strategy Pattern:");
            Console.WriteLine("      • الغرض: تغيير سلوك الكائن في وقت التشغيل");
            Console.WriteLine("      • الاستخدام: عندما نريد خوارزميات مختلفة لنفس المهمة");
            Console.WriteLine("      • المثال: طرق دفع متعددة (بطاقة، PayPal، تحويل)");
            
            Console.WriteLine("   🔄 State Pattern:");
            Console.WriteLine("      • الغرض: تغيير سلوك الكائن بناءً على حالته الداخلية");
            Console.WriteLine("      • الاستخدام: عندما يكون للكائن حالات متعددة ومعقدة");
            Console.WriteLine("      • المثال: دورة حياة الطلب (معلق، مؤكد، مشحون، مسلم)");
            
            Console.WriteLine("\n🏗️ الأنماط الهيكلية (Structural Patterns):");
            Console.WriteLine("   🎭 Facade Pattern:");
            Console.WriteLine("      • الغرض: تبسيط واجهة معقدة");
            Console.WriteLine("      • الاستخدام: عندما نريد إخفاء تعقيد النظام");
            Console.WriteLine("      • المثال: واجهة موحدة للمسرح المنزلي");
            
            Console.WriteLine("   🔌 Adapter Pattern:");
            Console.WriteLine("      • الغرض: جعل واجهات غير متوافقة تعمل معاً");
            Console.WriteLine("      • الاستخدام: عند دمج أنظمة قديمة مع جديدة");
            Console.WriteLine("      • المثال: توحيد أنظمة دفع مختلفة");
            
            Console.WriteLine("   ☕ Decorator Pattern:");
            Console.WriteLine("      • الغرض: إضافة وظائف جديدة للكائنات دون تعديل هيكلها");
            Console.WriteLine("      • الاستخدام: عندما نريد دمج وظائف متعددة بمرونة");
            Console.WriteLine("      • المثال: إضافات القهوة (حليب، سكر، شوكولاتة)");
            
            Console.WriteLine("   🔐 Proxy Pattern:");
            Console.WriteLine("      • الغرض: التحكم في الوصول إلى كائن آخر");
            Console.WriteLine("      • الاستخدام: للأمان، التخزين المؤقت، أو التحميل الكسول");
            Console.WriteLine("      • المثال: نظام إدارة المستندات مع صلاحيات");
            
            Console.WriteLine("\n🎯 الفوائد العامة لـ Design Patterns:");
            Console.WriteLine("   ✅ حلول مجربة ومختبرة");
            Console.WriteLine("   ✅ لغة مشتركة بين المطورين");
            Console.WriteLine("   ✅ تحسين جودة الكود وقابليته للصيانة");
            Console.WriteLine("   ✅ تسهيل إعادة الاستخدام والتوسع");
            Console.WriteLine("   ✅ اتباع مبادئ البرمجة الجيدة (SOLID)");
        }
    }
}
