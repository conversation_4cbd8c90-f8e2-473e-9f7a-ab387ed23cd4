namespace DesignPatterns.StructuralPatterns.Decorator
{
    /// <summary>
    /// أنواع القهوة الأساسية - Concrete Components
    /// هذه هي الكائنات الأساسية التي سنضيف إليها المكونات
    /// </summary>

    // إسبريسو
    public class Espresso : ICoffee
    {
        public string GetDescription()
        {
            return "إسبريسو";
        }

        public decimal GetCost()
        {
            return 8.00m; // 8 ريال
        }

        public NutritionInfo GetNutritionInfo()
        {
            return new NutritionInfo(calories: 5, caffeine: 95, sugar: 0, fat: 0);
        }
    }

    // أمريكانو
    public class Americano : ICoffee
    {
        public string GetDescription()
        {
            return "أمريكانو";
        }

        public decimal GetCost()
        {
            return 10.00m; // 10 ريال
        }

        public NutritionInfo GetNutritionInfo()
        {
            return new NutritionInfo(calories: 5, caffeine: 95, sugar: 0, fat: 0);
        }
    }

    // لاتيه
    public class Latte : ICoffee
    {
        public string GetDescription()
        {
            return "لاتيه";
        }

        public decimal GetCost()
        {
            return 15.00m; // 15 ريال
        }

        public NutritionInfo GetNutritionInfo()
        {
            return new NutritionInfo(calories: 120, caffeine: 75, sugar: 9, fat: 6);
        }
    }

    // كابتشينو
    public class Cappuccino : ICoffee
    {
        public string GetDescription()
        {
            return "كابتشينو";
        }

        public decimal GetCost()
        {
            return 12.00m; // 12 ريال
        }

        public NutritionInfo GetNutritionInfo()
        {
            return new NutritionInfo(calories: 80, caffeine: 75, sugar: 6, fat: 4);
        }
    }

    // موكا
    public class Mocha : ICoffee
    {
        public string GetDescription()
        {
            return "موكا";
        }

        public decimal GetCost()
        {
            return 18.00m; // 18 ريال
        }

        public NutritionInfo GetNutritionInfo()
        {
            return new NutritionInfo(calories: 200, caffeine: 85, sugar: 25, fat: 8);
        }
    }
}
