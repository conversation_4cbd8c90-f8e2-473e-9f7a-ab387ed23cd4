using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// عرض توضيحي لاستخدام State Pattern
    /// </summary>
    public class StateDemo
    {
        public static void RunDemo()
        {
            Console.WriteLine("=== State Pattern Demo ===\n");

            // === سيناريو 1: دورة حياة طلب عادية ===
            Console.WriteLine("🛍️ سيناريو 1: دورة حياة طلب عادية");
            Console.WriteLine(new string('=', 50));

            var order1 = new OrderContext("ORD-001", "أحمد محمد", 450.00m);
            order1.DisplayOrderInfo();

            Console.WriteLine("\n1️⃣ تأكيد الطلب:");
            order1.ConfirmOrder();
            order1.DisplayOrderInfo();

            Console.WriteLine("\n2️⃣ شحن الطلب:");
            order1.ShipOrder();
            order1.DisplayOrderInfo();

            Console.WriteLine("\n3️⃣ تسليم الطلب:");
            order1.DeliverOrder();
            order1.DisplayOrderInfo();

            // === سيناريو 2: إلغاء طلب ===
            Console.WriteLine("\n\n🚫 سيناريو 2: إلغاء طلب");
            Console.WriteLine(new string('=', 50));

            var order2 = new OrderContext("ORD-002", "فاطمة علي", 275.50m);
            order2.DisplayOrderInfo();

            Console.WriteLine("\n1️⃣ تأكيد الطلب:");
            order2.ConfirmOrder();

            Console.WriteLine("\n2️⃣ إلغاء الطلب بعد التأكيد:");
            order2.CancelOrder();
            order2.DisplayOrderInfo();

            // === سيناريو 3: إرجاع طلب ===
            Console.WriteLine("\n\n📦 سيناريو 3: إرجاع طلب");
            Console.WriteLine(new string('=', 50));

            var order3 = new OrderContext("ORD-003", "سارة أحمد", 320.75m);
            
            // تسريع دورة الحياة
            Console.WriteLine("\n⚡ تسريع دورة الحياة:");
            order3.ConfirmOrder();
            order3.ShipOrder();
            order3.DeliverOrder();
            order3.DisplayOrderInfo();

            Console.WriteLine("\n📤 إرجاع الطلب:");
            order3.ReturnOrder();
            order3.DisplayOrderInfo();

            // === سيناريو 4: اختبار العمليات غير المسموحة ===
            Console.WriteLine("\n\n❌ سيناريو 4: اختبار العمليات غير المسموحة");
            Console.WriteLine(new string('=', 50));

            var order4 = new OrderContext("ORD-004", "محمد علي", 180.25m);
            
            Console.WriteLine("\n🚫 محاولة شحن طلب غير مؤكد:");
            order4.ShipOrder();

            Console.WriteLine("\n🚫 محاولة تسليم طلب غير مؤكد:");
            order4.DeliverOrder();

            Console.WriteLine("\n🚫 محاولة إرجاع طلب غير مسلم:");
            order4.ReturnOrder();

            // === سيناريو 5: إلغاء طارئ أثناء الشحن ===
            Console.WriteLine("\n\n🚨 سيناريو 5: إلغاء طارئ أثناء الشحن");
            Console.WriteLine(new string('=', 50));

            var order5 = new OrderContext("ORD-005", "علي محمد", 520.00m);
            order5.ConfirmOrder();
            order5.ShipOrder();
            order5.DisplayOrderInfo();

            Console.WriteLine("\n🚨 إلغاء طارئ أثناء الشحن:");
            order5.CancelOrder();
            order5.DisplayOrderInfo();

            Console.WriteLine("\n✨ State Pattern يوفر:");
            Console.WriteLine("• فصل منطق كل حالة في كلاس منفصل");
            Console.WriteLine("• تغيير السلوك بناءً على الحالة الحالية");
            Console.WriteLine("• منع العمليات غير المسموحة في كل حالة");
            Console.WriteLine("• سهولة إضافة حالات جديدة");
            Console.WriteLine("• تجنب if/else statements معقدة");
        }
    }
}
