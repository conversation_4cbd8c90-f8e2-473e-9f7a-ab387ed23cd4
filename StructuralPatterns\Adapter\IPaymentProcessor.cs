namespace DesignPatterns.StructuralPatterns.Adapter
{
    /// <summary>
    /// الواجهة المطلوبة في نظامنا لمعالجة المدفوعات
    /// هذه هي الواجهة التي يتوقعها العميل (Target Interface)
    /// </summary>
    public interface IPaymentProcessor
    {
        /// <summary>
        /// معالجة الدفع
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="currency">العملة</param>
        /// <param name="cardNumber">رقم البطاقة</param>
        /// <returns>معرف المعاملة إذا نجحت، null إذا فشلت</returns>
        string ProcessPayment(decimal amount, string currency, string cardNumber);

        /// <summary>
        /// التحقق من صحة البطاقة
        /// </summary>
        /// <param name="cardNumber">رقم البطاقة</param>
        /// <returns>true إذا كانت البطاقة صحيحة</returns>
        bool ValidateCard(string cardNumber);

        /// <summary>
        /// الحصول على اسم معالج الدفع
        /// </summary>
        string GetProcessorName();
    }
}
