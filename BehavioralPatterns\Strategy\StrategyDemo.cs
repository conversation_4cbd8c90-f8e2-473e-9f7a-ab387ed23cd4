using System;

namespace DesignPatterns.BehavioralPatterns.Strategy
{
    /// <summary>
    /// عرض توضيحي لاستخدام Strategy Pattern
    /// </summary>
    public class StrategyDemo
    {
        public static void RunDemo()
        {
            Console.WriteLine("=== Strategy Pattern Demo ===\n");

            // إنشاء معالج الدفعات
            var paymentProcessor = new PaymentProcessor();

            // مبلغ الشراء
            decimal purchaseAmount = 250.75m;

            Console.WriteLine("🛍️ سيناريو: شراء منتجات بقيمة 250.75 ريال");
            Console.WriteLine("سنجرب طرق دفع مختلفة باستخدام Strategy Pattern\n");

            // === الدفع بالبطاقة الائتمانية ===
            Console.WriteLine("=== 1. الدفع بالبطاقة الائتمانية ===");
            var creditCardStrategy = new CreditCardStrategy(
                "****************", 
                "أحمد محمد", 
                "12/25", 
                "123"
            );
            
            paymentProcessor.SetPaymentStrategy(creditCardStrategy);
            paymentProcessor.ProcessPayment(purchaseAmount);
            Console.WriteLine();

            // === الدفع عبر PayPal ===
            Console.WriteLine("=== 2. الدفع عبر PayPal ===");
            var paypalStrategy = new PayPalStrategy("<EMAIL>", "mypassword123");
            
            paymentProcessor.SetPaymentStrategy(paypalStrategy);
            paymentProcessor.ProcessPayment(purchaseAmount);
            Console.WriteLine();

            // === الدفع بالتحويل البنكي ===
            Console.WriteLine("=== 3. الدفع بالتحويل البنكي ===");
            var bankTransferStrategy = new BankTransferStrategy(
                "**********", 
                "البنك الأهلي السعودي", 
                "فاطمة علي"
            );
            
            paymentProcessor.SetPaymentStrategy(bankTransferStrategy);
            paymentProcessor.ProcessPayment(purchaseAmount);
            Console.WriteLine();

            // === اختبار حالات الخطأ ===
            Console.WriteLine("=== 4. اختبار حالات الخطأ ===");
            
            // بطاقة منتهية الصلاحية
            Console.WriteLine("أ) بطاقة منتهية الصلاحية:");
            var expiredCardStrategy = new CreditCardStrategy(
                "****************", 
                "سارة أحمد", 
                "01/20", // منتهية الصلاحية
                "456"
            );
            
            paymentProcessor.SetPaymentStrategy(expiredCardStrategy);
            paymentProcessor.ProcessPayment(100);
            Console.WriteLine();

            // بيانات PayPal غير صحيحة
            Console.WriteLine("ب) بيانات PayPal غير صحيحة:");
            var invalidPaypalStrategy = new PayPalStrategy("invalid-email", "123"); // كلمة مرور قصيرة
            
            paymentProcessor.SetPaymentStrategy(invalidPaypalStrategy);
            paymentProcessor.ProcessPayment(100);
            Console.WriteLine();

            // === إظهار مرونة النمط ===
            Console.WriteLine("=== 5. مرونة تغيير الاستراتيجية ===");
            Console.WriteLine("يمكن تغيير طريقة الدفع في أي وقت دون تعديل الكود الأساسي:");
            
            var strategies = new IPaymentStrategy[]
            {
                new CreditCardStrategy("**********987654", "محمد علي", "06/26", "789"),
                new PayPalStrategy("<EMAIL>", "securepass"),
                new BankTransferStrategy("**********", "بنك الرياض", "علي محمد")
            };

            foreach (var strategy in strategies)
            {
                paymentProcessor.SetPaymentStrategy(strategy);
                Console.WriteLine($"طريقة الدفع الحالية: {paymentProcessor.GetCurrentPaymentMethod()}");
            }

            Console.WriteLine("\n✨ Strategy Pattern يسمح بـ:");
            Console.WriteLine("• تغيير السلوك في وقت التشغيل");
            Console.WriteLine("• إضافة طرق دفع جديدة بسهولة");
            Console.WriteLine("• فصل منطق كل طريقة دفع");
            Console.WriteLine("• تجنب if/else statements المعقدة");
        }
    }
}
