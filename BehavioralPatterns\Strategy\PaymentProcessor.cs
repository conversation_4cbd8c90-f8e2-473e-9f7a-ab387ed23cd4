using System;

namespace DesignPatterns.BehavioralPatterns.Strategy
{
    /// <summary>
    /// معالج الدفعات - Context في Strategy Pattern
    /// يستخدم استراتيجية الدفع المحددة لمعالجة الدفعات
    /// </summary>
    public class PaymentProcessor
    {
        private IPaymentStrategy _paymentStrategy;

        /// <summary>
        /// تعيين استراتيجية الدفع
        /// </summary>
        /// <param name="paymentStrategy">استراتيجية الدفع المراد استخدامها</param>
        public void SetPaymentStrategy(IPaymentStrategy paymentStrategy)
        {
            _paymentStrategy = paymentStrategy ?? throw new ArgumentNullException(nameof(paymentStrategy));
        }

        /// <summary>
        /// معالجة الدفع باستخدام الاستراتيجية المحددة
        /// </summary>
        /// <param name="amount">المبلغ المراد دفعه</param>
        /// <returns>true إذا تمت العملية بنجاح</returns>
        public bool ProcessPayment(decimal amount)
        {
            if (_paymentStrategy == null)
            {
                Console.WriteLine("❌ لم يتم تحديد طريقة الدفع");
                return false;
            }

            if (amount <= 0)
            {
                Console.WriteLine("❌ المبلغ يجب أن يكون أكبر من صفر");
                return false;
            }

            Console.WriteLine($"🛒 بدء معالجة دفع بقيمة {amount:C}");
            Console.WriteLine($"📋 طريقة الدفع المختارة: {_paymentStrategy.GetPaymentMethodName()}");
            Console.WriteLine(new string('-', 50));

            try
            {
                bool result = _paymentStrategy.ProcessPayment(amount);
                
                if (result)
                {
                    Console.WriteLine(new string('-', 50));
                    Console.WriteLine("🎉 تمت عملية الدفع بنجاح!");
                    LogTransaction(amount, _paymentStrategy.GetPaymentMethodName(), "نجح");
                }
                else
                {
                    Console.WriteLine(new string('-', 50));
                    Console.WriteLine("💥 فشلت عملية الدفع!");
                    LogTransaction(amount, _paymentStrategy.GetPaymentMethodName(), "فشل");
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ أثناء معالجة الدفع: {ex.Message}");
                LogTransaction(amount, _paymentStrategy.GetPaymentMethodName(), "خطأ");
                return false;
            }
        }

        /// <summary>
        /// تسجيل المعاملة في السجل
        /// </summary>
        private void LogTransaction(decimal amount, string paymentMethod, string status)
        {
            Console.WriteLine($"📝 تسجيل المعاملة: {DateTime.Now:yyyy-MM-dd HH:mm:ss} | {paymentMethod} | {amount:C} | {status}");
        }

        /// <summary>
        /// الحصول على معلومات طريقة الدفع الحالية
        /// </summary>
        public string GetCurrentPaymentMethod()
        {
            return _paymentStrategy?.GetPaymentMethodName() ?? "لم يتم تحديد طريقة دفع";
        }
    }
}
