namespace DesignPatterns.StructuralPatterns.Decorator
{
    /// <summary>
    /// واجهة القهوة الأساسية - Component Interface
    /// تحدد العمليات التي يمكن تنفيذها على القهوة
    /// </summary>
    public interface ICoffee
    {
        /// <summary>
        /// الحصول على وصف القهوة
        /// </summary>
        string GetDescription();

        /// <summary>
        /// حساب التكلفة الإجمالية
        /// </summary>
        decimal GetCost();

        /// <summary>
        /// الحصول على معلومات التغذية
        /// </summary>
        NutritionInfo GetNutritionInfo();
    }

    /// <summary>
    /// معلومات التغذية
    /// </summary>
    public class NutritionInfo
    {
        public int Calories { get; set; }
        public int Caffeine { get; set; } // بالملليجرام
        public int Sugar { get; set; } // بالجرام
        public int Fat { get; set; } // بالجرام

        public NutritionInfo(int calories = 0, int caffeine = 0, int sugar = 0, int fat = 0)
        {
            Calories = calories;
            Caffeine = caffeine;
            Sugar = sugar;
            Fat = fat;
        }

        public static NutritionInfo operator +(NutritionInfo a, NutritionInfo b)
        {
            return new NutritionInfo(
                a.Calories + b.Calories,
                a.Caffeine + b.Caffeine,
                a.Sugar + b.Sugar,
                a.Fat + b.Fat
            );
        }

        public override string ToString()
        {
            return $"السعرات: {Calories} | الكافيين: {Caffeine}mg | السكر: {Sugar}g | الدهون: {Fat}g";
        }
    }
}
