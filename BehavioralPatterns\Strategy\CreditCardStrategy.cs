using System;

namespace DesignPatterns.BehavioralPatterns.Strategy
{
    /// <summary>
    /// استراتيجية الدفع بالبطاقة الائتمانية
    /// </summary>
    public class CreditCardStrategy : IPaymentStrategy
    {
        private string _cardNumber;
        private string _cardHolderName;
        private string _expiryDate;
        private string _cvv;

        public CreditCardStrategy(string cardNumber, string cardHolderName, string expiryDate, string cvv)
        {
            _cardNumber = cardNumber;
            _cardHolderName = cardHolderName;
            _expiryDate = expiryDate;
            _cvv = cvv;
        }

        public bool ProcessPayment(decimal amount)
        {
            if (!ValidatePaymentData())
            {
                Console.WriteLine("❌ بيانات البطاقة الائتمانية غير صحيحة");
                return false;
            }

            Console.WriteLine($"💳 معالجة دفع بالبطاقة الائتمانية...");
            Console.WriteLine($"   رقم البطاقة: ****-****-****-{_cardNumber.Substring(_cardNumber.Length - 4)}");
            Console.WriteLine($"   اسم حامل البطاقة: {_cardHolderName}");
            Console.WriteLine($"   المبلغ: {amount:C}");
            
            // محاكاة معالجة الدفع
            System.Threading.Thread.Sleep(2000);
            
            Console.WriteLine("✅ تم الدفع بالبطاقة الائتمانية بنجاح!");
            return true;
        }

        public string GetPaymentMethodName()
        {
            return "البطاقة الائتمانية";
        }

        public bool ValidatePaymentData()
        {
            // التحقق من رقم البطاقة (يجب أن يكون 16 رقم)
            if (string.IsNullOrEmpty(_cardNumber) || _cardNumber.Length != 16)
                return false;

            // التحقق من اسم حامل البطاقة
            if (string.IsNullOrEmpty(_cardHolderName))
                return false;

            // التحقق من تاريخ الانتهاء (تنسيق MM/YY)
            if (string.IsNullOrEmpty(_expiryDate) || _expiryDate.Length != 5)
                return false;

            // التحقق من CVV (3 أرقام)
            if (string.IsNullOrEmpty(_cvv) || _cvv.Length != 3)
                return false;

            // التحقق من أن البطاقة لم تنته صلاحيتها
            try
            {
                var parts = _expiryDate.Split('/');
                var month = int.Parse(parts[0]);
                var year = int.Parse("20" + parts[1]);
                var expiryDate = new DateTime(year, month, 1).AddMonths(1).AddDays(-1);
                
                if (expiryDate < DateTime.Now)
                {
                    Console.WriteLine("❌ البطاقة منتهية الصلاحية");
                    return false;
                }
            }
            catch
            {
                return false;
            }

            return true;
        }
    }
}
