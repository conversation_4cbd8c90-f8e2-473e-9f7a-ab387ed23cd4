using System;

namespace DesignPatterns.StructuralPatterns.Proxy
{
    /// <summary>
    /// عرض توضيحي لاستخدام Proxy Pattern
    /// </summary>
    public class ProxyDemo
    {
        public static void RunDemo()
        {
            Console.WriteLine("=== Proxy Pattern Demo ===\n");

            // === إنشاء مستخدمين بصلاحيات مختلفة ===
            Console.WriteLine("👥 إنشاء مستخدمين بصلاحيات مختلفة:");
            Console.WriteLine(new string('=', 50));

            var admin = new UserInfo("admin", "Admin");
            var manager = new UserInfo("manager", "Manager", "ReadInternal", "ReadConfidential", "Write");
            var employee = new UserInfo("employee", "Employee", "ReadInternal");
            var guest = new UserInfo("guest", "Guest");

            Console.WriteLine($"👑 المدير: {admin.Username} - صلاحيات كاملة");
            Console.WriteLine($"👔 المدير التنفيذي: {manager.Username} - قراءة سرية + كتابة");
            Console.WriteLine($"👤 الموظف: {employee.Username} - قراءة داخلية فقط");
            Console.WriteLine($"👁️ الضيف: {guest.Username} - قراءة عامة فقط");

            // === سيناريو 1: المدير - صلاحيات كاملة ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("👑 سيناريو 1: المدير - صلاحيات كاملة");
            Console.WriteLine(new string('*', 70));

            var adminProxy = new DocumentServiceProxy(admin);
            
            Console.WriteLine("\n📋 عرض قائمة المستندات:");
            var documents = adminProxy.GetDocumentList();
            foreach (var doc in documents)
            {
                var info = adminProxy.GetDocumentInfo(doc);
                Console.WriteLine($"  📄 {info}");
            }

            Console.WriteLine("\n📖 قراءة مستند سري:");
            try
            {
                var secretContent = adminProxy.ReadDocument("DOC004");
                Console.WriteLine($"✅ المحتوى: {secretContent.Substring(0, Math.Min(50, secretContent.Length))}...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
            }

            // === سيناريو 2: الموظف - صلاحيات محدودة ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("👤 سيناريو 2: الموظف - صلاحيات محدودة");
            Console.WriteLine(new string('*', 70));

            var employeeProxy = new DocumentServiceProxy(employee);
            
            Console.WriteLine("\n📋 عرض قائمة المستندات (مفلترة):");
            var employeeDocuments = employeeProxy.GetDocumentList();
            foreach (var doc in employeeDocuments)
            {
                var info = employeeProxy.GetDocumentInfo(doc);
                Console.WriteLine($"  📄 {info}");
            }

            Console.WriteLine("\n🚫 محاولة قراءة مستند سري:");
            try
            {
                var secretContent = employeeProxy.ReadDocument("DOC004");
                Console.WriteLine($"✅ المحتوى: {secretContent}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
            }

            Console.WriteLine("\n✅ قراءة مستند داخلي:");
            try
            {
                var internalContent = employeeProxy.ReadDocument("DOC003");
                Console.WriteLine($"✅ المحتوى: {internalContent.Substring(0, Math.Min(50, internalContent.Length))}...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
            }

            // === سيناريو 3: الضيف - قراءة عامة فقط ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("👁️ سيناريو 3: الضيف - قراءة عامة فقط");
            Console.WriteLine(new string('*', 70));

            var guestProxy = new DocumentServiceProxy(guest);
            
            Console.WriteLine("\n📋 عرض قائمة المستندات (عامة فقط):");
            var guestDocuments = guestProxy.GetDocumentList();
            foreach (var doc in guestDocuments)
            {
                var info = guestProxy.GetDocumentInfo(doc);
                Console.WriteLine($"  📄 {info}");
            }

            // === سيناريو 4: اختبار التخزين المؤقت ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("⚡ سيناريو 4: اختبار التخزين المؤقت");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("\n🔄 قراءة نفس المستند مرتين:");
            
            // القراءة الأولى - من قاعدة البيانات
            Console.WriteLine("1️⃣ القراءة الأولى:");
            var content1 = adminProxy.ReadDocument("DOC001");
            
            // القراءة الثانية - من التخزين المؤقت
            Console.WriteLine("\n2️⃣ القراءة الثانية:");
            var content2 = adminProxy.ReadDocument("DOC001");
            
            Console.WriteLine($"✅ المحتوى متطابق: {content1 == content2}");

            // === سيناريو 5: اختبار الكتابة والحذف ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("✏️ سيناريو 5: اختبار الكتابة والحذف");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("\n📝 المدير ينشئ مستند جديد:");
            try
            {
                adminProxy.WriteDocument("DOC005", "مستند جديد تم إنشاؤه بواسطة المدير");
                Console.WriteLine("✅ تم إنشاء المستند بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
            }

            Console.WriteLine("\n🚫 الموظف يحاول حذف مستند:");
            try
            {
                employeeProxy.DeleteDocument("DOC005");
                Console.WriteLine("✅ تم حذف المستند");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
            }

            Console.WriteLine("\n🗑️ المدير يحذف المستند:");
            try
            {
                adminProxy.DeleteDocument("DOC005");
                Console.WriteLine("✅ تم حذف المستند بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
            }

            // === عرض الإحصائيات والسجلات ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("📊 الإحصائيات والسجلات");
            Console.WriteLine(new string('*', 70));

            adminProxy.ShowCacheStats();
            adminProxy.ShowAuditLog();

            // === مقارنة مع وبدون Proxy ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("📊 مقارنة: مع وبدون Proxy Pattern");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("\n❌ بدون Proxy Pattern:");
            Console.WriteLine("   • لا يوجد تحكم في الوصول");
            Console.WriteLine("   • لا يوجد تخزين مؤقت");
            Console.WriteLine("   • لا يوجد تسجيل للعمليات");
            Console.WriteLine("   • إنشاء فوري للكائنات المكلفة");
            Console.WriteLine("   • صعوبة في إضافة وظائف جديدة");

            Console.WriteLine("\n✅ مع Proxy Pattern:");
            Console.WriteLine("   • تحكم كامل في الوصول والأمان");
            Console.WriteLine("   • تحسين الأداء بالتخزين المؤقت");
            Console.WriteLine("   • تسجيل شامل للعمليات");
            Console.WriteLine("   • التحميل الكسول للموارد");
            Console.WriteLine("   • إمكانية إضافة وظائف دون تعديل الكود الأساسي");

            Console.WriteLine("\n🎯 أنواع Proxy Pattern:");
            Console.WriteLine("   • Virtual Proxy: التحميل الكسول");
            Console.WriteLine("   • Protection Proxy: التحكم في الوصول");
            Console.WriteLine("   • Caching Proxy: التخزين المؤقت");
            Console.WriteLine("   • Logging Proxy: تسجيل العمليات");
            Console.WriteLine("   • Remote Proxy: الوصول للكائنات البعيدة");

            Console.WriteLine("\n🏆 Proxy Pattern يحقق:");
            Console.WriteLine("• فصل الاهتمامات (Separation of Concerns)");
            Console.WriteLine("• تحسين الأداء والأمان");
            Console.WriteLine("• المرونة في إضافة وظائف جديدة");
            Console.WriteLine("• الشفافية للعميل (نفس الواجهة)");
        }
    }
}
