using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// حالة "مؤكد"
    /// الطلب تم تأكيده وجاهز للشحن
    /// </summary>
    public class ConfirmedState : IOrderState
    {
        public void ConfirmOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب مؤكد بالفعل");
        }

        public void ShipOrder(OrderContext context)
        {
            Console.WriteLine("📦 تم شحن الطلب!");
            Console.WriteLine("🚚 رقم الشحنة: SH" + DateTime.Now.Ticks.ToString().Substring(0, 8));
            Console.WriteLine("📱 سيتم إرسال رسالة نصية بتفاصيل الشحن");
            context.SetState(new ShippedState());
        }

        public void DeliverOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن تسليم الطلب قبل شحنه");
        }

        public void CancelOrder(OrderContext context)
        {
            Console.WriteLine("🚫 تم إلغاء الطلب المؤكد");
            Console.WriteLine("⚠️ قد تطبق رسوم إلغاء بسبب تأكيد الطلب");
            Console.WriteLine("💰 سيتم استرداد المبلغ خلال 5-7 أيام عمل");
            context.SetState(new CancelledState());
        }

        public void ReturnOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن إرجاع طلب لم يتم تسليمه بعد");
        }

        public string GetStateName()
        {
            return "مؤكد";
        }

        public string[] GetAvailableActions()
        {
            return new[] { "شحن الطلب", "إلغاء الطلب" };
        }
    }
}
