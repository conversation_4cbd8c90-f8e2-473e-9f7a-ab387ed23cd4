namespace DesignPatterns.CreationalPatterns.Prototype
{
    /// <summary>
    /// كلاس العنوان - مثال على كائن معقد يحتاج نسخ عميق
    /// </summary>
    public class Address : IPrototype<Address>
    {
        public string Street { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }

        public Address() { }

        public Address(string street, string city, string country, string postalCode)
        {
            Street = street;
            City = city;
            Country = country;
            PostalCode = postalCode;
        }

        /// <summary>
        /// نسخ عميق للعنوان
        /// </summary>
        public Address Clone()
        {
            return new Address
            {
                Street = this.Street,
                City = this.City,
                Country = this.Country,
                PostalCode = this.PostalCode
            };
        }

        public override string ToString()
        {
            return $"{Street}, {City}, {Country} - {PostalCode}";
        }
    }
}
