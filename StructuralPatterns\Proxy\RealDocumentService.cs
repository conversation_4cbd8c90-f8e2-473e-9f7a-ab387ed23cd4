using System;
using System.Collections.Generic;
using System.Threading;

namespace DesignPatterns.StructuralPatterns.Proxy
{
    /// <summary>
    /// خدمة المستندات الحقيقية - Real Subject
    /// تحتوي على المنطق الفعلي لإدارة المستندات
    /// </summary>
    public class RealDocumentService : IDocumentService
    {
        private readonly Dictionary<string, string> _documents;
        private readonly Dictionary<string, DocumentInfo> _documentInfos;

        public RealDocumentService()
        {
            _documents = new Dictionary<string, string>();
            _documentInfos = new Dictionary<string, DocumentInfo>();
            
            // تهيئة بعض المستندات التجريبية
            InitializeSampleDocuments();
        }

        /// <summary>
        /// تهيئة مستندات تجريبية
        /// </summary>
        private void InitializeSampleDocuments()
        {
            var sampleDocs = new[]
            {
                new { Id = "DOC001", Title = "تقرير المبيعات الشهري", Author = "أحمد محمد", Content = "تقرير مفصل عن مبيعات الشهر الماضي...", AccessLevel = "Public" },
                new { Id = "DOC002", Title = "استراتيجية الشركة 2024", Author = "فاطمة علي", Content = "الخطة الاستراتيجية للشركة للعام القادم...", AccessLevel = "Confidential" },
                new { Id = "DOC003", Title = "دليل الموظفين", Author = "سارة أحمد", Content = "دليل شامل لسياسات وإجراءات الشركة...", AccessLevel = "Internal" },
                new { Id = "DOC004", Title = "البيانات المالية السرية", Author = "محمد علي", Content = "تقرير مالي سري للإدارة العليا...", AccessLevel = "TopSecret" }
            };

            foreach (var doc in sampleDocs)
            {
                _documents[doc.Id] = doc.Content;
                _documentInfos[doc.Id] = new DocumentInfo
                {
                    Id = doc.Id,
                    Title = doc.Title,
                    Author = doc.Author,
                    CreatedDate = DateTime.Now.AddDays(-new Random().Next(1, 30)),
                    LastModified = DateTime.Now.AddDays(-new Random().Next(1, 10)),
                    SizeInBytes = doc.Content.Length * 2, // تقدير تقريبي
                    AccessLevel = doc.AccessLevel
                };
            }
        }

        public string ReadDocument(string documentId)
        {
            Console.WriteLine($"🔍 RealDocumentService: قراءة المستند {documentId}");
            
            // محاكاة عملية قراءة مكلفة (قاعدة بيانات، ملف، إلخ)
            Thread.Sleep(1000);
            
            if (_documents.ContainsKey(documentId))
            {
                Console.WriteLine($"✅ تم تحميل المستند {documentId} من قاعدة البيانات");
                return _documents[documentId];
            }
            
            throw new ArgumentException($"المستند {documentId} غير موجود");
        }

        public void WriteDocument(string documentId, string content)
        {
            Console.WriteLine($"✏️ RealDocumentService: كتابة المستند {documentId}");
            
            // محاكاة عملية كتابة مكلفة
            Thread.Sleep(800);
            
            _documents[documentId] = content;
            
            // تحديث معلومات المستند
            if (_documentInfos.ContainsKey(documentId))
            {
                _documentInfos[documentId].LastModified = DateTime.Now;
                _documentInfos[documentId].SizeInBytes = content.Length * 2;
            }
            else
            {
                _documentInfos[documentId] = new DocumentInfo
                {
                    Id = documentId,
                    Title = $"مستند {documentId}",
                    Author = "مستخدم النظام",
                    CreatedDate = DateTime.Now,
                    LastModified = DateTime.Now,
                    SizeInBytes = content.Length * 2,
                    AccessLevel = "Internal"
                };
            }
            
            Console.WriteLine($"✅ تم حفظ المستند {documentId} في قاعدة البيانات");
        }

        public void DeleteDocument(string documentId)
        {
            Console.WriteLine($"🗑️ RealDocumentService: حذف المستند {documentId}");
            
            // محاكاة عملية حذف
            Thread.Sleep(500);
            
            if (_documents.ContainsKey(documentId))
            {
                _documents.Remove(documentId);
                _documentInfos.Remove(documentId);
                Console.WriteLine($"✅ تم حذف المستند {documentId} من قاعدة البيانات");
            }
            else
            {
                throw new ArgumentException($"المستند {documentId} غير موجود");
            }
        }

        public List<string> GetDocumentList()
        {
            Console.WriteLine("📋 RealDocumentService: الحصول على قائمة المستندات");
            
            // محاكاة عملية استعلام قاعدة البيانات
            Thread.Sleep(600);
            
            var documentList = new List<string>(_documents.Keys);
            Console.WriteLine($"✅ تم العثور على {documentList.Count} مستند");
            
            return documentList;
        }

        public DocumentInfo GetDocumentInfo(string documentId)
        {
            Console.WriteLine($"ℹ️ RealDocumentService: الحصول على معلومات المستند {documentId}");
            
            // محاكاة عملية استعلام
            Thread.Sleep(300);
            
            if (_documentInfos.ContainsKey(documentId))
            {
                Console.WriteLine($"✅ تم العثور على معلومات المستند {documentId}");
                return _documentInfos[documentId];
            }
            
            throw new ArgumentException($"المستند {documentId} غير موجود");
        }
    }
}
