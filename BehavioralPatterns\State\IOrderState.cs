namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// واجهة حالة الطلب
    /// تحدد العمليات التي يمكن تنفيذها في كل حالة
    /// </summary>
    public interface IOrderState
    {
        /// <summary>
        /// تأكيد الطلب
        /// </summary>
        void ConfirmOrder(OrderContext context);

        /// <summary>
        /// شحن الطلب
        /// </summary>
        void ShipOrder(OrderContext context);

        /// <summary>
        /// تسليم الطلب
        /// </summary>
        void DeliverOrder(OrderContext context);

        /// <summary>
        /// إلغاء الطلب
        /// </summary>
        void CancelOrder(OrderContext context);

        /// <summary>
        /// إرجاع الطلب
        /// </summary>
        void ReturnOrder(OrderContext context);

        /// <summary>
        /// الحصول على اسم الحالة
        /// </summary>
        string GetStateName();

        /// <summary>
        /// الحصول على العمليات المتاحة في هذه الحالة
        /// </summary>
        string[] GetAvailableActions();
    }
}
