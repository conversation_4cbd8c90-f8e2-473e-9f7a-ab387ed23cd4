using System;
using System.Collections.Generic;

namespace DesignPatterns.CreationalPatterns.Prototype
{
    /// <summary>
    /// مثال عملي على Prototype Pattern - كلاس الموظف
    /// يمثل موظف في شركة مع معلومات شخصية ومهارات
    /// </summary>
    public class Employee : IPrototype<Employee>
    {
        public string Name { get; set; }
        public string Department { get; set; }
        public decimal Salary { get; set; }
        public DateTime HireDate { get; set; }
        public List<string> Skills { get; set; }
        public Address Address { get; set; }

        public Employee()
        {
            Skills = new List<string>();
            Address = new Address();
        }

        /// <summary>
        /// Constructor مع المعاملات الأساسية
        /// </summary>
        public Employee(string name, string department, decimal salary)
        {
            Name = name;
            Department = department;
            Salary = salary;
            HireDate = DateTime.Now;
            Skills = new List<string>();
            Address = new Address();
        }

        /// <summary>
        /// تنفيذ الـ Clone method لإنشاء نسخة عميقة
        /// هذا مهم جداً لتجنب مشاكل الـ Shallow Copy
        /// </summary>
        public Employee Clone()
        {
            // إنشاء موظف جديد
            var clonedEmployee = new Employee
            {
                Name = this.Name,
                Department = this.Department,
                Salary = this.Salary,
                HireDate = this.HireDate,
                
                // نسخ عميقة للمهارات (لأنها List)
                Skills = new List<string>(this.Skills),
                
                // نسخ عميقة للعنوان
                Address = this.Address?.Clone()
            };

            return clonedEmployee;
        }

        /// <summary>
        /// إضافة مهارة جديدة للموظف
        /// </summary>
        public void AddSkill(string skill)
        {
            if (!Skills.Contains(skill))
            {
                Skills.Add(skill);
            }
        }

        /// <summary>
        /// عرض معلومات الموظف
        /// </summary>
        public override string ToString()
        {
            return $"الموظف: {Name}\n" +
                   $"القسم: {Department}\n" +
                   $"الراتب: {Salary:C}\n" +
                   $"تاريخ التوظيف: {HireDate:yyyy-MM-dd}\n" +
                   $"المهارات: {string.Join(", ", Skills)}\n" +
                   $"العنوان: {Address}\n";
        }
    }
}
