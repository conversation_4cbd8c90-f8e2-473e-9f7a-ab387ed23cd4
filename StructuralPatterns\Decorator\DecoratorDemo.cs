using System;

namespace DesignPatterns.StructuralPatterns.Decorator
{
    /// <summary>
    /// عرض توضيحي لاستخدام Decorator Pattern
    /// </summary>
    public class DecoratorDemo
    {
        public static void RunDemo()
        {
            Console.WriteLine("=== Decorator Pattern Demo ===\n");

            var coffeeShop = new CoffeeShop();

            // عرض القوائم
            coffeeShop.ShowCoffeeMenu();
            coffeeShop.ShowAddonsMenu();

            // === سيناريو 1: طلب بسيط ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("☕ سيناريو 1: طلب إسبريسو بسيط");
            Console.WriteLine(new string('*', 70));

            var simpleEspresso = coffeeShop.CreateCustomCoffee("إسبريسو");
            coffeeShop.ProcessOrder(simpleEspresso);

            // === سيناريو 2: طلب معقد ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("🍫 سيناريو 2: لاتيه فاخر بإضافات متعددة");
            Console.WriteLine(new string('*', 70));

            var fancyLatte = coffeeShop.CreateCustomCoffee("لاتيه", 
                "حليب", "شوكولاتة", "كريمة", "فانيليا", "سكر");
            coffeeShop.ProcessOrder(fancyLatte);

            // === سيناريو 3: بناء القهوة خطوة بخطوة ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("🔧 سيناريو 3: بناء القهوة خطوة بخطوة");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("🏗️ بناء موكا مخصصة:");
            
            // البداية بالقهوة الأساسية
            ICoffee coffee = new Mocha();
            Console.WriteLine($"1️⃣ البداية: {coffee.GetDescription()} - {coffee.GetCost():C}");

            // إضافة الحليب
            coffee = new MilkDecorator(coffee);
            Console.WriteLine($"2️⃣ + حليب: {coffee.GetDescription()} - {coffee.GetCost():C}");

            // إضافة شوت إضافي
            coffee = new ExtraEspressoDecorator(coffee);
            Console.WriteLine($"3️⃣ + شوت إضافي: {coffee.GetDescription()} - {coffee.GetCost():C}");

            // إضافة الكريمة
            coffee = new WhippedCreamDecorator(coffee);
            Console.WriteLine($"4️⃣ + كريمة: {coffee.GetDescription()} - {coffee.GetCost():C}");

            // إضافة القرفة
            coffee = new CinnamonDecorator(coffee);
            Console.WriteLine($"5️⃣ + قرفة: {coffee.GetDescription()} - {coffee.GetCost():C}");

            Console.WriteLine($"\n🎯 النتيجة النهائية: {coffee.GetDescription()}");
            Console.WriteLine($"💰 التكلفة الإجمالية: {coffee.GetCost():C}");
            Console.WriteLine($"🍎 معلومات التغذية: {coffee.GetNutritionInfo()}");

            // === عرض الوصفات الشائعة ===
            coffeeShop.ShowPopularRecipes();

            // === مقارنة بين أنواع مختلفة ===
            coffeeShop.CompareCoffees();

            // === إظهار مرونة النمط ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("🔄 مرونة Decorator Pattern");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("\n🎯 يمكن دمج الإضافات بأي ترتيب:");
            
            // نفس الإضافات بترتيب مختلف
            var coffee1 = new SugarDecorator(new MilkDecorator(new Espresso()));
            var coffee2 = new MilkDecorator(new SugarDecorator(new Espresso()));
            
            Console.WriteLine($"☕ إسبريسو + حليب + سكر: {coffee1.GetDescription()} - {coffee1.GetCost():C}");
            Console.WriteLine($"☕ إسبريسو + سكر + حليب: {coffee2.GetDescription()} - {coffee2.GetCost():C}");

            // === مقارنة مع وبدون Decorator ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("📊 مقارنة: مع وبدون Decorator Pattern");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("\n❌ بدون Decorator Pattern:");
            Console.WriteLine("   • نحتاج كلاس منفصل لكل تركيبة ممكنة");
            Console.WriteLine("   • EspressoWithMilk, EspressoWithMilkAndSugar, إلخ...");
            Console.WriteLine("   • انفجار في عدد الكلاسات (Combinatorial Explosion)");
            Console.WriteLine("   • صعوبة في إضافة مكونات جديدة");

            Console.WriteLine("\n✅ مع Decorator Pattern:");
            Console.WriteLine("   • كلاس واحد لكل إضافة");
            Console.WriteLine("   • إمكانية دمج الإضافات بأي ترتيب");
            Console.WriteLine("   • سهولة إضافة مكونات جديدة");
            Console.WriteLine("   • مرونة في وقت التشغيل");

            Console.WriteLine("\n🔢 مثال على انفجار الكلاسات:");
            Console.WriteLine("   مع 5 أنواع قهوة و 8 إضافات:");
            Console.WriteLine("   • بدون Decorator: 5 × 2^8 = 1,280 كلاس محتمل!");
            Console.WriteLine("   • مع Decorator: 5 + 8 = 13 كلاس فقط!");

            Console.WriteLine("\n🏆 Decorator Pattern يحقق:");
            Console.WriteLine("• إضافة وظائف دون تعديل الكود الموجود");
            Console.WriteLine("• مرونة في التركيب والدمج");
            Console.WriteLine("• اتباع مبدأ Open/Closed Principle");
            Console.WriteLine("• تجنب انفجار عدد الكلاسات");
        }
    }
}
