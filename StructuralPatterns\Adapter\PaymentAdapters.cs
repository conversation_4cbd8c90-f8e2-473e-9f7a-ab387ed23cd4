using System;

namespace DesignPatterns.StructuralPatterns.Adapter
{
    /// <summary>
    /// محولات أنظمة الدفع - Adapters
    /// تحول واجهات الأنظمة القديمة إلى الواجهة المطلوبة في نظامنا
    /// </summary>

    // محول البنك الأهلي
    public class AlAhliPaymentAdapter : IPaymentProcessor
    {
        private readonly AlAhliLegacyPaymentSystem _legacySystem;

        public AlAhliPaymentAdapter(AlAhliLegacyPaymentSystem legacySystem)
        {
            _legacySystem = legacySystem ?? throw new ArgumentNullException(nameof(legacySystem));
        }

        public string ProcessPayment(decimal amount, string currency, string cardNumber)
        {
            try
            {
                // تحويل العملة إلى ريال سعودي إذا لزم الأمر
                double amountInSAR = ConvertToSAR(amount, currency);
                
                // استخدام النظام القديم
                bool success = _legacySystem.AuthorizePayment(amountInSAR, cardNumber);
                
                if (success)
                {
                    return _legacySystem.GenerateTransactionId();
                }
                
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في محول البنك الأهلي: {ex.Message}");
                return null;
            }
        }

        public bool ValidateCard(string cardNumber)
        {
            return _legacySystem.CheckCardValidity(cardNumber);
        }

        public string GetProcessorName()
        {
            return "البنك الأهلي السعودي (محول)";
        }

        private double ConvertToSAR(decimal amount, string currency)
        {
            // محاكاة تحويل العملة
            double rate = currency.ToUpper() switch
            {
                "USD" => 3.75,
                "EUR" => 4.10,
                "SAR" => 1.0,
                _ => 1.0
            };
            
            return (double)amount * rate;
        }
    }

    // محول بنك الرياض
    public class RiyadBankAdapter : IPaymentProcessor
    {
        private readonly RiyadBankOldSystem _oldSystem;

        public RiyadBankAdapter(RiyadBankOldSystem oldSystem)
        {
            _oldSystem = oldSystem ?? throw new ArgumentNullException(nameof(oldSystem));
        }

        public string ProcessPayment(decimal amount, string currency, string cardNumber)
        {
            try
            {
                // تحويل إلى float كما يتطلب النظام القديم
                float amountInSAR = (float)ConvertToSAR(amount, currency);
                
                // استخدام النظام القديم
                int statusCode = _oldSystem.ExecuteTransaction(amountInSAR, cardNumber);
                
                if (statusCode == 0) // 0 يعني نجح
                {
                    return _oldSystem.CreateTransactionReference();
                }
                
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في محول بنك الرياض: {ex.Message}");
                return null;
            }
        }

        public bool ValidateCard(string cardNumber)
        {
            return _oldSystem.IsCardAccepted(cardNumber);
        }

        public string GetProcessorName()
        {
            return "بنك الرياض (محول)";
        }

        private decimal ConvertToSAR(decimal amount, string currency)
        {
            // نفس منطق التحويل
            decimal rate = currency.ToUpper() switch
            {
                "USD" => 3.75m,
                "EUR" => 4.10m,
                "SAR" => 1.0m,
                _ => 1.0m
            };
            
            return amount * rate;
        }
    }

    // محول البوابة الدولية
    public class InternationalPaymentAdapter : IPaymentProcessor
    {
        private readonly InternationalPaymentGateway _gateway;

        public InternationalPaymentAdapter(InternationalPaymentGateway gateway)
        {
            _gateway = gateway ?? throw new ArgumentNullException(nameof(gateway));
        }

        public string ProcessPayment(decimal amount, string currency, string cardNumber)
        {
            try
            {
                // إنشاء طلب الدفع بالتنسيق المطلوب
                var request = new PaymentRequest
                {
                    Amount = amount,
                    CurrencyCode = currency.ToUpper(),
                    CardNumber = cardNumber
                };
                
                // استخدام البوابة الدولية
                var result = _gateway.SubmitPayment(request);
                
                if (result.IsSuccessful)
                {
                    return result.TransactionId;
                }
                
                Console.WriteLine($"فشل الدفع: {result.ResponseMessage}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في محول البوابة الدولية: {ex.Message}");
                return null;
            }
        }

        public bool ValidateCard(string cardNumber)
        {
            return _gateway.ValidateCreditCard(cardNumber, "USD"); // افتراض USD للفحص
        }

        public string GetProcessorName()
        {
            return "البوابة الدولية للمدفوعات (محول)";
        }
    }
}
