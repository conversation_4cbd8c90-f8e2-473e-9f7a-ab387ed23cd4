using System;

namespace DesignPatterns.BehavioralPatterns.Strategy
{
    /// <summary>
    /// استراتيجية الدفع عبر PayPal
    /// </summary>
    public class PayPalStrategy : IPaymentStrategy
    {
        private string _email;
        private string _password;

        public PayPalStrategy(string email, string password)
        {
            _email = email;
            _password = password;
        }

        public bool ProcessPayment(decimal amount)
        {
            if (!ValidatePaymentData())
            {
                Console.WriteLine("❌ بيانات PayPal غير صحيحة");
                return false;
            }

            Console.WriteLine($"🅿️ معالجة دفع عبر PayPal...");
            Console.WriteLine($"   البريد الإلكتروني: {_email}");
            Console.WriteLine($"   المبلغ: {amount:C}");
            
            // محاكاة تسجيل الدخول
            Console.WriteLine("   🔐 تسجيل الدخول إلى PayPal...");
            System.Threading.Thread.Sleep(1500);
            
            // محاكاة معالجة الدفع
            Console.WriteLine("   💰 معالجة الدفع...");
            System.Threading.Thread.Sleep(2000);
            
            Console.WriteLine("✅ تم الدفع عبر PayPal بنجاح!");
            return true;
        }

        public string GetPaymentMethodName()
        {
            return "PayPal";
        }

        public bool ValidatePaymentData()
        {
            // التحقق من البريد الإلكتروني
            if (string.IsNullOrEmpty(_email) || !_email.Contains("@"))
                return false;

            // التحقق من كلمة المرور
            if (string.IsNullOrEmpty(_password) || _password.Length < 6)
                return false;

            return true;
        }
    }
}
