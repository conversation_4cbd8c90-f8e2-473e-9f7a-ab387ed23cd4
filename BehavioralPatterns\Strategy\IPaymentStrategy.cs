namespace DesignPatterns.BehavioralPatterns.Strategy
{
    /// <summary>
    /// واجهة استراتيجية الدفع
    /// تحدد العقد الذي يجب على جميع طرق الدفع تنفيذه
    /// </summary>
    public interface IPaymentStrategy
    {
        /// <summary>
        /// تنفيذ عملية الدفع
        /// </summary>
        /// <param name="amount">المبلغ المراد دفعه</param>
        /// <returns>true إذا تمت العملية بنجاح، false إذا فشلت</returns>
        bool ProcessPayment(decimal amount);

        /// <summary>
        /// الحصول على اسم طريقة الدفع
        /// </summary>
        string GetPaymentMethodName();

        /// <summary>
        /// التحقق من صحة بيانات الدفع
        /// </summary>
        bool ValidatePaymentData();
    }
}
