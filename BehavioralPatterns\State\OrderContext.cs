using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// سياق الطلب - Context في State Pattern
    /// يحتوي على الحالة الحالية ويفوض العمليات إليها
    /// </summary>
    public class OrderContext
    {
        private IOrderState _currentState;
        
        public string OrderId { get; private set; }
        public DateTime OrderDate { get; private set; }
        public decimal TotalAmount { get; private set; }
        public string CustomerName { get; private set; }

        public OrderContext(string orderId, string customerName, decimal totalAmount)
        {
            OrderId = orderId;
            CustomerName = customerName;
            TotalAmount = totalAmount;
            OrderDate = DateTime.Now;
            
            // الحالة الأولية هي "في انتظار التأكيد"
            _currentState = new PendingState();
            
            Console.WriteLine($"📦 تم إنشاء طلب جديد:");
            Console.WriteLine($"   رقم الطلب: {OrderId}");
            Console.WriteLine($"   العميل: {CustomerName}");
            Console.WriteLine($"   المبلغ: {TotalAmount:C}");
            Console.WriteLine($"   الحالة: {_currentState.GetStateName()}");
        }

        /// <summary>
        /// تغيير حالة الطلب
        /// </summary>
        public void SetState(IOrderState newState)
        {
            var oldStateName = _currentState?.GetStateName() ?? "غير محدد";
            _currentState = newState;
            
            Console.WriteLine($"🔄 تغيير حالة الطلب {OrderId}:");
            Console.WriteLine($"   من: {oldStateName}");
            Console.WriteLine($"   إلى: {_currentState.GetStateName()}");
        }

        /// <summary>
        /// الحصول على الحالة الحالية
        /// </summary>
        public IOrderState GetCurrentState()
        {
            return _currentState;
        }

        // تفويض العمليات إلى الحالة الحالية
        public void ConfirmOrder() => _currentState.ConfirmOrder(this);
        public void ShipOrder() => _currentState.ShipOrder(this);
        public void DeliverOrder() => _currentState.DeliverOrder(this);
        public void CancelOrder() => _currentState.CancelOrder(this);
        public void ReturnOrder() => _currentState.ReturnOrder(this);

        /// <summary>
        /// عرض معلومات الطلب الحالية
        /// </summary>
        public void DisplayOrderInfo()
        {
            Console.WriteLine($"\n📋 معلومات الطلب {OrderId}:");
            Console.WriteLine($"   العميل: {CustomerName}");
            Console.WriteLine($"   تاريخ الطلب: {OrderDate:yyyy-MM-dd HH:mm}");
            Console.WriteLine($"   المبلغ: {TotalAmount:C}");
            Console.WriteLine($"   الحالة الحالية: {_currentState.GetStateName()}");
            
            var availableActions = _currentState.GetAvailableActions();
            if (availableActions.Length > 0)
            {
                Console.WriteLine($"   العمليات المتاحة: {string.Join(", ", availableActions)}");
            }
            else
            {
                Console.WriteLine($"   العمليات المتاحة: لا توجد عمليات متاحة");
            }
        }
    }
}
