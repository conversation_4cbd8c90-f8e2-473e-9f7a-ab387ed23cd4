using System;

namespace DesignPatterns.CreationalPatterns.Prototype
{
    /// <summary>
    /// واجهة الـ Prototype الأساسية
    /// تحدد العقد الذي يجب على جميع الكائنات القابلة للنسخ تنفيذه
    /// </summary>
    /// <typeparam name="T">نوع الكائن الذي سيتم نسخه</typeparam>
    public interface IPrototype<T>
    {
        /// <summary>
        /// ينشئ نسخة عميقة من الكائن الحالي
        /// </summary>
        /// <returns>نسخة جديدة من الكائن</returns>
        T Clone();
    }
}
