using System;
using System.Collections.Generic;
using System.Linq;

namespace DesignPatterns.StructuralPatterns.Proxy
{
    /// <summary>
    /// وكيل خدمة المستندات - Proxy
    /// يضيف طبقات الأمان، التخزين المؤقت، والتسجيل
    /// </summary>
    public class DocumentServiceProxy : IDocumentService
    {
        private RealDocumentService _realService;
        private readonly UserInfo _currentUser;
        private readonly Dictionary<string, CachedDocument> _cache;
        private readonly List<string> _auditLog;

        public DocumentServiceProxy(UserInfo currentUser)
        {
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _cache = new Dictionary<string, CachedDocument>();
            _auditLog = new List<string>();
            
            Console.WriteLine($"🔐 تم إنشاء Proxy للمستخدم: {_currentUser.Username} ({_currentUser.Role})");
        }

        /// <summary>
        /// التحميل الكسول للخدمة الحقيقية
        /// </summary>
        private RealDocumentService GetRealService()
        {
            if (_realService == null)
            {
                Console.WriteLine("⚡ Lazy Loading: إنشاء خدمة المستندات الحقيقية");
                _realService = new RealDocumentService();
            }
            return _realService;
        }

        public string ReadDocument(string documentId)
        {
            LogOperation($"محاولة قراءة المستند {documentId}");

            // التحقق من الصلاحيات
            if (!HasReadPermission(documentId))
            {
                var message = $"❌ ليس لديك صلاحية لقراءة المستند {documentId}";
                LogOperation(message);
                throw new UnauthorizedAccessException(message);
            }

            // التحقق من التخزين المؤقت
            if (_cache.ContainsKey(documentId) && !_cache[documentId].IsExpired())
            {
                Console.WriteLine($"⚡ Cache Hit: تم العثور على المستند {documentId} في التخزين المؤقت");
                LogOperation($"تم قراءة المستند {documentId} من التخزين المؤقت");
                return _cache[documentId].Content;
            }

            // قراءة من الخدمة الحقيقية
            Console.WriteLine($"💾 Cache Miss: قراءة المستند {documentId} من قاعدة البيانات");
            var content = GetRealService().ReadDocument(documentId);
            
            // حفظ في التخزين المؤقت
            _cache[documentId] = new CachedDocument(content, TimeSpan.FromMinutes(5));
            
            LogOperation($"تم قراءة المستند {documentId} بنجاح");
            return content;
        }

        public void WriteDocument(string documentId, string content)
        {
            LogOperation($"محاولة كتابة المستند {documentId}");

            // التحقق من الصلاحيات
            if (!HasWritePermission(documentId))
            {
                var message = $"❌ ليس لديك صلاحية لكتابة المستند {documentId}";
                LogOperation(message);
                throw new UnauthorizedAccessException(message);
            }

            // التحقق من صحة المحتوى
            if (string.IsNullOrWhiteSpace(content))
            {
                throw new ArgumentException("محتوى المستند لا يمكن أن يكون فارغاً");
            }

            // كتابة في الخدمة الحقيقية
            GetRealService().WriteDocument(documentId, content);
            
            // تحديث التخزين المؤقت
            _cache[documentId] = new CachedDocument(content, TimeSpan.FromMinutes(5));
            
            LogOperation($"تم كتابة المستند {documentId} بنجاح");
        }

        public void DeleteDocument(string documentId)
        {
            LogOperation($"محاولة حذف المستند {documentId}");

            // التحقق من الصلاحيات
            if (!HasDeletePermission(documentId))
            {
                var message = $"❌ ليس لديك صلاحية لحذف المستند {documentId}";
                LogOperation(message);
                throw new UnauthorizedAccessException(message);
            }

            // حذف من الخدمة الحقيقية
            GetRealService().DeleteDocument(documentId);
            
            // إزالة من التخزين المؤقت
            _cache.Remove(documentId);
            
            LogOperation($"تم حذف المستند {documentId} بنجاح");
        }

        public List<string> GetDocumentList()
        {
            LogOperation("محاولة الحصول على قائمة المستندات");

            // الحصول على القائمة الكاملة
            var allDocuments = GetRealService().GetDocumentList();
            
            // تصفية المستندات بناءً على الصلاحيات
            var accessibleDocuments = allDocuments
                .Where(docId => HasReadPermission(docId))
                .ToList();
            
            LogOperation($"تم عرض {accessibleDocuments.Count} من أصل {allDocuments.Count} مستند");
            
            return accessibleDocuments;
        }

        public DocumentInfo GetDocumentInfo(string documentId)
        {
            LogOperation($"محاولة الحصول على معلومات المستند {documentId}");

            // التحقق من الصلاحيات
            if (!HasReadPermission(documentId))
            {
                var message = $"❌ ليس لديك صلاحية لعرض معلومات المستند {documentId}";
                LogOperation(message);
                throw new UnauthorizedAccessException(message);
            }

            var info = GetRealService().GetDocumentInfo(documentId);
            LogOperation($"تم عرض معلومات المستند {documentId}");
            
            return info;
        }

        /// <summary>
        /// التحقق من صلاحية القراءة
        /// </summary>
        private bool HasReadPermission(string documentId)
        {
            // المدير له صلاحية على كل شيء
            if (_currentUser.Role == "Admin")
                return true;

            // الحصول على معلومات المستند للتحقق من مستوى الوصول
            try
            {
                var docInfo = GetRealService().GetDocumentInfo(documentId);
                
                return docInfo.AccessLevel switch
                {
                    "Public" => true,
                    "Internal" => _currentUser.HasPermission("ReadInternal"),
                    "Confidential" => _currentUser.HasPermission("ReadConfidential"),
                    "TopSecret" => _currentUser.HasPermission("ReadTopSecret"),
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صلاحية الكتابة
        /// </summary>
        private bool HasWritePermission(string documentId)
        {
            return _currentUser.Role == "Admin" || _currentUser.HasPermission("Write");
        }

        /// <summary>
        /// التحقق من صلاحية الحذف
        /// </summary>
        private bool HasDeletePermission(string documentId)
        {
            return _currentUser.Role == "Admin" || _currentUser.HasPermission("Delete");
        }

        /// <summary>
        /// تسجيل العمليات
        /// </summary>
        private void LogOperation(string operation)
        {
            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {_currentUser.Username}: {operation}";
            _auditLog.Add(logEntry);
            Console.WriteLine($"📝 Log: {logEntry}");
        }

        /// <summary>
        /// عرض سجل العمليات
        /// </summary>
        public void ShowAuditLog()
        {
            Console.WriteLine("\n📋 سجل العمليات:");
            Console.WriteLine(new string('-', 50));
            
            if (!_auditLog.Any())
            {
                Console.WriteLine("لا توجد عمليات مسجلة");
                return;
            }

            foreach (var entry in _auditLog.TakeLast(10)) // آخر 10 عمليات
            {
                Console.WriteLine(entry);
            }
        }

        /// <summary>
        /// عرض إحصائيات التخزين المؤقت
        /// </summary>
        public void ShowCacheStats()
        {
            Console.WriteLine("\n📊 إحصائيات التخزين المؤقت:");
            Console.WriteLine(new string('-', 40));
            Console.WriteLine($"عدد المستندات المخزنة: {_cache.Count}");
            
            var expiredCount = _cache.Values.Count(c => c.IsExpired());
            Console.WriteLine($"المستندات المنتهية الصلاحية: {expiredCount}");
            Console.WriteLine($"المستندات الصالحة: {_cache.Count - expiredCount}");
        }
    }

    /// <summary>
    /// مستند مخزن مؤقتاً
    /// </summary>
    internal class CachedDocument
    {
        public string Content { get; }
        public DateTime ExpiryTime { get; }

        public CachedDocument(string content, TimeSpan cacheTime)
        {
            Content = content;
            ExpiryTime = DateTime.Now.Add(cacheTime);
        }

        public bool IsExpired()
        {
            return DateTime.Now > ExpiryTime;
        }
    }
}
