# دليل البدء السريع 🚀

## تشغيل المشروع في 3 خطوات

### 1️⃣ تحقق من متطلبات النظام
```bash
# تحقق من وجود .NET
dotnet --version
# يجب أن يكون 6.0 أو أحدث
```

### 2️⃣ انتقل إلى مجلد المشروع
```bash
cd "Design Pattern"
```

### 3️⃣ شغل البرنامج
```bash
dotnet run
```

## ماذا ستشاهد؟

ستظهر لك قائمة تفاعلية مثل هذه:

```
📋 القائمة الرئيسية - اختر النمط الذي تريد تعلمه:
────────────────────────────────────────────────────────────

🏗️  الأنماط الإنشائية (Creational Patterns):
   1️⃣  Prototype Pattern - نمط النموذج الأولي

🎭 الأنماط السلوكية (Behavioral Patterns):
   2️⃣  Strategy Pattern - نمط الاستراتيجية
   3️⃣  State Pattern - نمط الحالة

🏗️  الأنماط الهيكلية (Structural Patterns):
   4️⃣  Facade Pattern - نمط الواجهة
   5️⃣  Adapter Pattern - نمط المحول
   6️⃣  Decorator Pattern - نمط المزخرف
   7️⃣  Proxy Pattern - نمط الوكيل

🎪 خيارات إضافية:
   8️⃣  تشغيل جميع الأمثلة
   9️⃣  ملخص جميع الأنماط
   0️⃣  خروج

👉 اختر رقم النمط (0-9):
```

## نصائح للاستخدام

### 🎯 للمبتدئين
- ابدأ بالرقم **9** لقراءة ملخص جميع الأنماط
- ثم جرب الأنماط واحداً تلو الآخر (1-7)
- اقرأ التعليقات في الكود لفهم التفاصيل

### 🔥 للمتقدمين
- استخدم الرقم **8** لتشغيل جميع الأمثلة دفعة واحدة
- ادرس الكود المصدري في المجلدات المختلفة
- جرب تعديل الأمثلة وإضافة ميزات جديدة

### 💡 للمدرسين
- استخدم كل نمط كدرس منفصل
- اطلب من الطلاب تشغيل الأمثلة وشرحها
- استخدم الأمثلة كنقطة انطلاق لمشاريع أكبر

## الأنماط بالتفصيل

### 🏗️ Prototype Pattern
**المشكلة**: إنشاء كائنات معقدة مكلف  
**الحل**: نسخ كائنات موجودة  
**المثال**: نظام إدارة الموظفين

### 🎯 Strategy Pattern
**المشكلة**: تغيير السلوك في وقت التشغيل  
**الحل**: فصل الخوارزميات في كلاسات منفصلة  
**المثال**: طرق دفع متعددة

### 🔄 State Pattern
**المشكلة**: سلوك معقد يعتمد على الحالة  
**الحل**: كلاس منفصل لكل حالة  
**المثال**: دورة حياة الطلب

### 🎭 Facade Pattern
**المشكلة**: واجهة معقدة صعبة الاستخدام  
**الحل**: واجهة مبسطة موحدة  
**المثال**: نظام المسرح المنزلي

### 🔌 Adapter Pattern
**المشكلة**: واجهات غير متوافقة  
**الحل**: محول يوحد الواجهات  
**المثال**: أنظمة دفع مختلفة

### ☕ Decorator Pattern
**المشكلة**: إضافة وظائف دون تعديل الكود  
**الحل**: تغليف الكائنات بوظائف إضافية  
**المثال**: إضافات القهوة

### 🔐 Proxy Pattern
**المشكلة**: التحكم في الوصول للكائنات  
**الحل**: كائن وسيط يتحكم في الوصول  
**المثال**: نظام إدارة المستندات

## حل المشاكل الشائعة

### ❌ خطأ: "dotnet command not found"
**الحل**: قم بتثبيت .NET SDK من [هنا](https://dotnet.microsoft.com/download)

### ❌ خطأ: "Project file not found"
**الحل**: تأكد من أنك في المجلد الصحيح الذي يحتوي على `DesignPatterns.csproj`

### ❌ خطأ: "Encoding issues"
**الحل**: تأكد من أن terminal يدعم UTF-8 للنصوص العربية

### ❌ البرنامج لا يعرض النصوص العربية بشكل صحيح
**الحل**: 
- في Windows: استخدم Windows Terminal
- في VS Code: تأكد من إعداد UTF-8
- في Visual Studio: تأكد من إعدادات Console

## التطوير والتخصيص

### إضافة نمط جديد
1. أنشئ مجلد جديد في المجلد المناسب
2. أضف الكلاسات المطلوبة
3. أنشئ كلاس Demo
4. أضف النمط إلى `Program.cs`

### تعديل الأمثلة الموجودة
1. انتقل إلى مجلد النمط المطلوب
2. عدل الكلاسات حسب الحاجة
3. اختبر التغييرات بتشغيل البرنامج

## الموارد الإضافية

- [توثيق .NET](https://docs.microsoft.com/dotnet/)
- [كتاب Design Patterns](https://refactoring.guru/design-patterns)
- [أمثلة إضافية](https://github.com/topics/design-patterns)

---

**نصيحة**: لا تحاول تعلم جميع الأنماط دفعة واحدة. ركز على نمط واحد في كل مرة وتأكد من فهمه جيداً قبل الانتقال للتالي! 🎯
