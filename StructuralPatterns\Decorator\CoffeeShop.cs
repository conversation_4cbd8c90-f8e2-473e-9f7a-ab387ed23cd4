using System;
using System.Collections.Generic;

namespace DesignPatterns.StructuralPatterns.Decorator
{
    /// <summary>
    /// محل القهوة - يستخدم Decorator Pattern لبناء طلبات القهوة المخصصة
    /// </summary>
    public class CoffeeShop
    {
        private Dictionary<string, Func<ICoffee>> _coffeeMenu;
        private Dictionary<string, Func<ICoffee, ICoffee>> _addonsMenu;

        public CoffeeShop()
        {
            InitializeCoffeeMenu();
            InitializeAddonsMenu();
        }

        /// <summary>
        /// تهيئة قائمة أنواع القهوة الأساسية
        /// </summary>
        private void InitializeCoffeeMenu()
        {
            _coffeeMenu = new Dictionary<string, Func<ICoffee>>
            {
                { "إسبريسو", () => new Espresso() },
                { "أمريكانو", () => new Americano() },
                { "لاتيه", () => new Latte() },
                { "كابتشينو", () => new Cappuccino() },
                { "موكا", () => new Mocha() }
            };
        }

        /// <summary>
        /// تهيئة قائمة الإضافات
        /// </summary>
        private void InitializeAddonsMenu()
        {
            _addonsMenu = new Dictionary<string, Func<ICoffee, ICoffee>>
            {
                { "حليب", coffee => new MilkDecorator(coffee) },
                { "سكر", coffee => new SugarDecorator(coffee, 1) },
                { "سكر مضاعف", coffee => new SugarDecorator(coffee, 2) },
                { "كريمة", coffee => new WhippedCreamDecorator(coffee) },
                { "شوكولاتة", coffee => new ChocolateDecorator(coffee) },
                { "فانيليا", coffee => new VanillaDecorator(coffee) },
                { "قرفة", coffee => new CinnamonDecorator(coffee) },
                { "شوت إضافي", coffee => new ExtraEspressoDecorator(coffee) }
            };
        }

        /// <summary>
        /// عرض قائمة القهوة
        /// </summary>
        public void ShowCoffeeMenu()
        {
            Console.WriteLine("☕ قائمة أنواع القهوة:");
            Console.WriteLine(new string('-', 30));
            
            foreach (var coffee in _coffeeMenu)
            {
                var coffeeInstance = coffee.Value();
                Console.WriteLine($"• {coffee.Key} - {coffeeInstance.GetCost():C}");
            }
        }

        /// <summary>
        /// عرض قائمة الإضافات
        /// </summary>
        public void ShowAddonsMenu()
        {
            Console.WriteLine("\n🧁 قائمة الإضافات:");
            Console.WriteLine(new string('-', 30));
            
            // إنشاء قهوة وهمية لحساب تكلفة الإضافات
            var baseCoffee = new Espresso();
            
            foreach (var addon in _addonsMenu)
            {
                var decoratedCoffee = addon.Value(baseCoffee);
                var addonCost = decoratedCoffee.GetCost() - baseCoffee.GetCost();
                Console.WriteLine($"• {addon.Key} - {addonCost:C}");
            }
        }

        /// <summary>
        /// إنشاء طلب قهوة مخصص
        /// </summary>
        /// <param name="coffeeType">نوع القهوة الأساسي</param>
        /// <param name="addons">قائمة الإضافات</param>
        /// <returns>القهوة المخصصة</returns>
        public ICoffee CreateCustomCoffee(string coffeeType, params string[] addons)
        {
            // التحقق من وجود نوع القهوة
            if (!_coffeeMenu.ContainsKey(coffeeType))
            {
                throw new ArgumentException($"نوع القهوة '{coffeeType}' غير متوفر");
            }

            // إنشاء القهوة الأساسية
            ICoffee coffee = _coffeeMenu[coffeeType]();

            // إضافة المكونات واحداً تلو الآخر
            foreach (var addon in addons)
            {
                if (_addonsMenu.ContainsKey(addon))
                {
                    coffee = _addonsMenu[addon](coffee);
                }
                else
                {
                    Console.WriteLine($"⚠️ تحذير: الإضافة '{addon}' غير متوفرة");
                }
            }

            return coffee;
        }

        /// <summary>
        /// معالجة طلب وعرض التفاصيل
        /// </summary>
        /// <param name="coffee">القهوة المطلوبة</param>
        public void ProcessOrder(ICoffee coffee)
        {
            Console.WriteLine("\n" + new string('=', 50));
            Console.WriteLine("🧾 فاتورة الطلب");
            Console.WriteLine(new string('=', 50));
            
            Console.WriteLine($"📝 الوصف: {coffee.GetDescription()}");
            Console.WriteLine($"💰 التكلفة الإجمالية: {coffee.GetCost():C}");
            
            var nutrition = coffee.GetNutritionInfo();
            Console.WriteLine($"🍎 معلومات التغذية: {nutrition}");
            
            Console.WriteLine(new string('=', 50));
            Console.WriteLine("✅ تم تحضير طلبكم - استمتعوا بقهوتكم!");
        }

        /// <summary>
        /// إنشاء وصفات قهوة شائعة
        /// </summary>
        public void ShowPopularRecipes()
        {
            Console.WriteLine("\n🌟 وصفات القهوة الشائعة:");
            Console.WriteLine(new string('=', 40));

            // لاتيه بالفانيليا
            var vanillaLatte = CreateCustomCoffee("لاتيه", "فانيليا", "سكر");
            Console.WriteLine($"\n☕ لاتيه بالفانيليا:");
            Console.WriteLine($"   {vanillaLatte.GetDescription()}");
            Console.WriteLine($"   التكلفة: {vanillaLatte.GetCost():C}");

            // موكا بالكريمة
            var creamyMocha = CreateCustomCoffee("موكا", "كريمة", "شوكولاتة");
            Console.WriteLine($"\n🍫 موكا بالكريمة:");
            Console.WriteLine($"   {creamyMocha.GetDescription()}");
            Console.WriteLine($"   التكلفة: {creamyMocha.GetCost():C}");

            // كابتشينو بالقرفة
            var cinnamonCappuccino = CreateCustomCoffee("كابتشينو", "قرفة", "سكر");
            Console.WriteLine($"\n🌿 كابتشينو بالقرفة:");
            Console.WriteLine($"   {cinnamonCappuccino.GetDescription()}");
            Console.WriteLine($"   التكلفة: {cinnamonCappuccino.GetCost():C}");

            // إسبريسو قوي
            var strongEspresso = CreateCustomCoffee("إسبريسو", "شوت إضافي", "سكر");
            Console.WriteLine($"\n💪 إسبريسو قوي:");
            Console.WriteLine($"   {strongEspresso.GetDescription()}");
            Console.WriteLine($"   التكلفة: {strongEspresso.GetCost():C}");
        }

        /// <summary>
        /// مقارنة بين طلبات مختلفة
        /// </summary>
        public void CompareCoffees()
        {
            Console.WriteLine("\n📊 مقارنة بين أنواع القهوة:");
            Console.WriteLine(new string('=', 60));

            var coffees = new[]
            {
                CreateCustomCoffee("إسبريسو"),
                CreateCustomCoffee("لاتيه", "حليب", "سكر"),
                CreateCustomCoffee("موكا", "كريمة", "شوكولاتة", "فانيليا")
            };

            foreach (var coffee in coffees)
            {
                Console.WriteLine($"\n☕ {coffee.GetDescription()}");
                Console.WriteLine($"   💰 التكلفة: {coffee.GetCost():C}");
                Console.WriteLine($"   🍎 التغذية: {coffee.GetNutritionInfo()}");
            }
        }
    }
}
