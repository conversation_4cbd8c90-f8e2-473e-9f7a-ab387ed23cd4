using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// حالة "في انتظار التأكيد"
    /// الحالة الأولية للطلب بعد إنشائه
    /// </summary>
    public class PendingState : IOrderState
    {
        public void ConfirmOrder(OrderContext context)
        {
            Console.WriteLine("✅ تم تأكيد الطلب بنجاح!");
            Console.WriteLine("📧 تم إرسال إيميل تأكيد للعميل");
            context.SetState(new ConfirmedState());
        }

        public void ShipOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن شحن الطلب قبل تأكيده");
        }

        public void DeliverOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن تسليم الطلب قبل تأكيده وشحنه");
        }

        public void CancelOrder(OrderContext context)
        {
            Console.WriteLine("🚫 تم إلغاء الطلب");
            Console.WriteLine("💰 سيتم استرداد المبلغ خلال 3-5 أيام عمل");
            context.SetState(new CancelledState());
        }

        public void ReturnOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن إرجاع طلب لم يتم تسليمه بعد");
        }

        public string GetStateName()
        {
            return "في انتظار التأكيد";
        }

        public string[] GetAvailableActions()
        {
            return new[] { "تأكيد الطلب", "إلغاء الطلب" };
        }
    }
}
