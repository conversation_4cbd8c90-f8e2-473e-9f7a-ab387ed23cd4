using System;

namespace DesignPatterns.BehavioralPatterns.Strategy
{
    /// <summary>
    /// استراتيجية الدفع بالتحويل البنكي
    /// </summary>
    public class BankTransferStrategy : IPaymentStrategy
    {
        private string _accountNumber;
        private string _bankName;
        private string _accountHolderName;

        public BankTransferStrategy(string accountNumber, string bankName, string accountHolderName)
        {
            _accountNumber = accountNumber;
            _bankName = bankName;
            _accountHolderName = accountHolderName;
        }

        public bool ProcessPayment(decimal amount)
        {
            if (!ValidatePaymentData())
            {
                Console.WriteLine("❌ بيانات التحويل البنكي غير صحيحة");
                return false;
            }

            Console.WriteLine($"🏦 معالجة دفع بالتحويل البنكي...");
            Console.WriteLine($"   البنك: {_bankName}");
            Console.WriteLine($"   رقم الحساب: ****{_accountNumber.Substring(_accountNumber.Length - 4)}");
            Console.WriteLine($"   اسم صاحب الحساب: {_accountHolderName}");
            Console.WriteLine($"   المبلغ: {amount:C}");
            
            // محاكاة التحقق من الحساب
            Console.WriteLine("   🔍 التحقق من بيانات الحساب...");
            System.Threading.Thread.Sleep(2000);
            
            // محاكاة معالجة التحويل
            Console.WriteLine("   💸 تنفيذ التحويل البنكي...");
            System.Threading.Thread.Sleep(3000);
            
            Console.WriteLine("✅ تم التحويل البنكي بنجاح!");
            Console.WriteLine("📧 سيتم إرسال إشعار بالتحويل خلال 24 ساعة");
            return true;
        }

        public string GetPaymentMethodName()
        {
            return "التحويل البنكي";
        }

        public bool ValidatePaymentData()
        {
            // التحقق من رقم الحساب
            if (string.IsNullOrEmpty(_accountNumber) || _accountNumber.Length < 10)
                return false;

            // التحقق من اسم البنك
            if (string.IsNullOrEmpty(_bankName))
                return false;

            // التحقق من اسم صاحب الحساب
            if (string.IsNullOrEmpty(_accountHolderName))
                return false;

            return true;
        }
    }
}
