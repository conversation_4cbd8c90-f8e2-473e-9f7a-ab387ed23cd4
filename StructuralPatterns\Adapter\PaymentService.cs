using System;
using System.Collections.Generic;
using System.Linq;

namespace DesignPatterns.StructuralPatterns.Adapter
{
    /// <summary>
    /// خدمة الدفع الموحدة - تستخدم جميع معالجات الدفع بنفس الطريقة
    /// بفضل Adapter Pattern
    /// </summary>
    public class PaymentService
    {
        private readonly List<IPaymentProcessor> _paymentProcessors;

        public PaymentService()
        {
            _paymentProcessors = new List<IPaymentProcessor>();
        }

        /// <summary>
        /// إضافة معالج دفع جديد
        /// </summary>
        public void AddPaymentProcessor(IPaymentProcessor processor)
        {
            _paymentProcessors.Add(processor);
            Console.WriteLine($"✅ تم إضافة معالج دفع: {processor.GetProcessorName()}");
        }

        /// <summary>
        /// معالجة الدفع باستخدام أفضل معالج متاح
        /// </summary>
        public string ProcessPayment(decimal amount, string currency, string cardNumber)
        {
            Console.WriteLine($"\n💳 بدء معالجة دفع: {amount} {currency}");
            Console.WriteLine($"   رقم البطاقة: {cardNumber.Substring(0, 4)}****");
            Console.WriteLine(new string('-', 50));

            if (!_paymentProcessors.Any())
            {
                Console.WriteLine("❌ لا توجد معالجات دفع متاحة");
                return null;
            }

            // البحث عن معالج مناسب للبطاقة
            var suitableProcessors = _paymentProcessors
                .Where(p => p.ValidateCard(cardNumber))
                .ToList();

            if (!suitableProcessors.Any())
            {
                Console.WriteLine("❌ لا يوجد معالج دفع يدعم هذا النوع من البطاقات");
                return null;
            }

            // محاولة المعالجة مع كل معالج حتى ينجح أحدهم
            foreach (var processor in suitableProcessors)
            {
                Console.WriteLine($"\n🔄 محاولة المعالجة مع: {processor.GetProcessorName()}");
                
                string transactionId = processor.ProcessPayment(amount, currency, cardNumber);
                
                if (!string.IsNullOrEmpty(transactionId))
                {
                    Console.WriteLine($"✅ نجح الدفع! معرف المعاملة: {transactionId}");
                    LogTransaction(transactionId, processor.GetProcessorName(), amount, currency);
                    return transactionId;
                }
                
                Console.WriteLine($"❌ فشل الدفع مع {processor.GetProcessorName()}");
            }

            Console.WriteLine("❌ فشل الدفع مع جميع المعالجات المتاحة");
            return null;
        }

        /// <summary>
        /// التحقق من صحة البطاقة مع جميع المعالجات
        /// </summary>
        public List<string> GetSupportedProcessors(string cardNumber)
        {
            var supportedProcessors = new List<string>();

            foreach (var processor in _paymentProcessors)
            {
                if (processor.ValidateCard(cardNumber))
                {
                    supportedProcessors.Add(processor.GetProcessorName());
                }
            }

            return supportedProcessors;
        }

        /// <summary>
        /// عرض جميع معالجات الدفع المتاحة
        /// </summary>
        public void ShowAvailableProcessors()
        {
            Console.WriteLine("\n📋 معالجات الدفع المتاحة:");
            Console.WriteLine(new string('-', 40));

            if (!_paymentProcessors.Any())
            {
                Console.WriteLine("لا توجد معالجات دفع مسجلة");
                return;
            }

            for (int i = 0; i < _paymentProcessors.Count; i++)
            {
                Console.WriteLine($"{i + 1}. {_paymentProcessors[i].GetProcessorName()}");
            }
        }

        /// <summary>
        /// تسجيل المعاملة
        /// </summary>
        private void LogTransaction(string transactionId, string processor, decimal amount, string currency)
        {
            Console.WriteLine($"\n📝 تسجيل المعاملة:");
            Console.WriteLine($"   معرف المعاملة: {transactionId}");
            Console.WriteLine($"   المعالج: {processor}");
            Console.WriteLine($"   المبلغ: {amount} {currency}");
            Console.WriteLine($"   التاريخ: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        }

        /// <summary>
        /// اختبار جميع المعالجات
        /// </summary>
        public void TestAllProcessors(string cardNumber)
        {
            Console.WriteLine($"\n🧪 اختبار جميع المعالجات للبطاقة: {cardNumber.Substring(0, 4)}****");
            Console.WriteLine(new string('-', 60));

            foreach (var processor in _paymentProcessors)
            {
                Console.WriteLine($"\n🔍 اختبار: {processor.GetProcessorName()}");
                bool isValid = processor.ValidateCard(cardNumber);
                Console.WriteLine($"   صحة البطاقة: {(isValid ? "✅ مقبولة" : "❌ غير مقبولة")}");
            }
        }
    }
}
