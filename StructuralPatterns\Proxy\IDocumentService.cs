using System;
using System.Collections.Generic;

namespace DesignPatterns.StructuralPatterns.Proxy
{
    /// <summary>
    /// واجهة خدمة المستندات - Subject Interface
    /// تحدد العمليات التي يمكن تنفيذها على المستندات
    /// </summary>
    public interface IDocumentService
    {
        /// <summary>
        /// قراءة مستند
        /// </summary>
        /// <param name="documentId">معرف المستند</param>
        /// <returns>محتوى المستند</returns>
        string ReadDocument(string documentId);

        /// <summary>
        /// كتابة مستند
        /// </summary>
        /// <param name="documentId">معرف المستند</param>
        /// <param name="content">المحتوى</param>
        void WriteDocument(string documentId, string content);

        /// <summary>
        /// حذف مستند
        /// </summary>
        /// <param name="documentId">معرف المستند</param>
        void DeleteDocument(string documentId);

        /// <summary>
        /// الحصول على قائمة المستندات
        /// </summary>
        /// <returns>قائمة بمعرفات المستندات</returns>
        List<string> GetDocumentList();

        /// <summary>
        /// الحصول على معلومات المستند
        /// </summary>
        /// <param name="documentId">معرف المستند</param>
        /// <returns>معلومات المستند</returns>
        DocumentInfo GetDocumentInfo(string documentId);
    }

    /// <summary>
    /// معلومات المستند
    /// </summary>
    public class DocumentInfo
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Author { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastModified { get; set; }
        public long SizeInBytes { get; set; }
        public string AccessLevel { get; set; }

        public override string ToString()
        {
            return $"المستند: {Title} | المؤلف: {Author} | الحجم: {SizeInBytes} بايت | مستوى الوصول: {AccessLevel}";
        }
    }

    /// <summary>
    /// معلومات المستخدم
    /// </summary>
    public class UserInfo
    {
        public string Username { get; set; }
        public string Role { get; set; }
        public List<string> Permissions { get; set; }

        public UserInfo(string username, string role, params string[] permissions)
        {
            Username = username;
            Role = role;
            Permissions = new List<string>(permissions);
        }

        public bool HasPermission(string permission)
        {
            return Permissions.Contains(permission) || Role == "Admin";
        }
    }
}
