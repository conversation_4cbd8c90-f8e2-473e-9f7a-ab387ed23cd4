namespace DesignPatterns.StructuralPatterns.Decorator
{
    /// <summary>
    /// إضافات القهوة - Concrete Decorators
    /// كل إضافة تضيف وظيفة أو خاصية جديدة للقهوة
    /// </summary>

    // إضافة الحليب
    public class MilkDecorator : CoffeeDecorator
    {
        public MilkDecorator(ICoffee coffee) : base(coffee) { }

        public override string GetDescription()
        {
            return base.GetDescription() + " + حليب";
        }

        public override decimal GetCost()
        {
            return base.GetCost() + 2.00m; // إضافة 2 ريال
        }

        public override NutritionInfo GetNutritionInfo()
        {
            var baseNutrition = base.GetNutritionInfo();
            var milkNutrition = new NutritionInfo(calories: 42, caffeine: 0, sugar: 5, fat: 2);
            return baseNutrition + milkNutrition;
        }
    }

    // إضافة السكر
    public class SugarDecorator : CoffeeDecorator
    {
        private readonly int _teaspoons;

        public SugarDecorator(ICoffee coffee, int teaspoons = 1) : base(coffee)
        {
            _teaspoons = teaspoons;
        }

        public override string GetDescription()
        {
            return base.GetDescription() + $" + سكر ({_teaspoons} ملعقة)";
        }

        public override decimal GetCost()
        {
            return base.GetCost() + (_teaspoons * 0.50m); // 0.5 ريال لكل ملعقة
        }

        public override NutritionInfo GetNutritionInfo()
        {
            var baseNutrition = base.GetNutritionInfo();
            var sugarNutrition = new NutritionInfo(
                calories: _teaspoons * 16, 
                caffeine: 0, 
                sugar: _teaspoons * 4, 
                fat: 0
            );
            return baseNutrition + sugarNutrition;
        }
    }

    // إضافة الكريمة
    public class WhippedCreamDecorator : CoffeeDecorator
    {
        public WhippedCreamDecorator(ICoffee coffee) : base(coffee) { }

        public override string GetDescription()
        {
            return base.GetDescription() + " + كريمة مخفوقة";
        }

        public override decimal GetCost()
        {
            return base.GetCost() + 3.50m; // إضافة 3.5 ريال
        }

        public override NutritionInfo GetNutritionInfo()
        {
            var baseNutrition = base.GetNutritionInfo();
            var creamNutrition = new NutritionInfo(calories: 52, caffeine: 0, sugar: 1, fat: 5);
            return baseNutrition + creamNutrition;
        }
    }

    // إضافة الشوكولاتة
    public class ChocolateDecorator : CoffeeDecorator
    {
        public ChocolateDecorator(ICoffee coffee) : base(coffee) { }

        public override string GetDescription()
        {
            return base.GetDescription() + " + شوكولاتة";
        }

        public override decimal GetCost()
        {
            return base.GetCost() + 4.00m; // إضافة 4 ريال
        }

        public override NutritionInfo GetNutritionInfo()
        {
            var baseNutrition = base.GetNutritionInfo();
            var chocolateNutrition = new NutritionInfo(calories: 70, caffeine: 12, sugar: 8, fat: 4);
            return baseNutrition + chocolateNutrition;
        }
    }

    // إضافة الفانيليا
    public class VanillaDecorator : CoffeeDecorator
    {
        public VanillaDecorator(ICoffee coffee) : base(coffee) { }

        public override string GetDescription()
        {
            return base.GetDescription() + " + فانيليا";
        }

        public override decimal GetCost()
        {
            return base.GetCost() + 2.50m; // إضافة 2.5 ريال
        }

        public override NutritionInfo GetNutritionInfo()
        {
            var baseNutrition = base.GetNutritionInfo();
            var vanillaNutrition = new NutritionInfo(calories: 30, caffeine: 0, sugar: 7, fat: 0);
            return baseNutrition + vanillaNutrition;
        }
    }

    // إضافة القرفة
    public class CinnamonDecorator : CoffeeDecorator
    {
        public CinnamonDecorator(ICoffee coffee) : base(coffee) { }

        public override string GetDescription()
        {
            return base.GetDescription() + " + قرفة";
        }

        public override decimal GetCost()
        {
            return base.GetCost() + 1.50m; // إضافة 1.5 ريال
        }

        public override NutritionInfo GetNutritionInfo()
        {
            var baseNutrition = base.GetNutritionInfo();
            var cinnamonNutrition = new NutritionInfo(calories: 6, caffeine: 0, sugar: 2, fat: 0);
            return baseNutrition + cinnamonNutrition;
        }
    }

    // إضافة شوت إسبريسو إضافي
    public class ExtraEspressoDecorator : CoffeeDecorator
    {
        public ExtraEspressoDecorator(ICoffee coffee) : base(coffee) { }

        public override string GetDescription()
        {
            return base.GetDescription() + " + شوت إسبريسو إضافي";
        }

        public override decimal GetCost()
        {
            return base.GetCost() + 5.00m; // إضافة 5 ريال
        }

        public override NutritionInfo GetNutritionInfo()
        {
            var baseNutrition = base.GetNutritionInfo();
            var espressoNutrition = new NutritionInfo(calories: 5, caffeine: 95, sugar: 0, fat: 0);
            return baseNutrition + espressoNutrition;
        }
    }
}
