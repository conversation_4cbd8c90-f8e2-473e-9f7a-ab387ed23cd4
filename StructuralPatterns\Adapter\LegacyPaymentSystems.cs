using System;

namespace DesignPatterns.StructuralPatterns.Adapter
{
    /// <summary>
    /// أنظمة الدفع القديمة (Legacy Systems) - Adaptees
    /// هذه الأنظمة لها واجهات مختلفة عن الواجهة المطلوبة في نظامنا
    /// </summary>

    // نظام دفع قديم - البنك الأهلي
    public class AlAhliLegacyPaymentSystem
    {
        public bool AuthorizePayment(double amountInSAR, string creditCardNo)
        {
            Console.WriteLine($"🏦 البنك الأهلي: تفويض دفع {amountInSAR} ريال");
            Console.WriteLine($"   رقم البطاقة: ****{creditCardNo.Substring(creditCardNo.Length - 4)}");
            
            // محاكاة معالجة
            System.Threading.Thread.Sleep(1000);
            
            // محاكاة نجاح العملية (90% نجاح)
            bool success = new Random().Next(1, 11) <= 9;
            
            if (success)
            {
                Console.WriteLine("✅ تم تفويض الدفع من البنك الأهلي");
                return true;
            }
            else
            {
                Console.WriteLine("❌ فشل تفويض الدفع من البنك الأهلي");
                return false;
            }
        }

        public string GenerateTransactionId()
        {
            return "AHL-" + DateTime.Now.Ticks.ToString().Substring(0, 8);
        }

        public bool CheckCardValidity(string cardNo)
        {
            Console.WriteLine($"🔍 البنك الأهلي: فحص صحة البطاقة {cardNo.Substring(0, 4)}****");
            
            // محاكاة فحص البطاقة
            return cardNo.Length == 16 && cardNo.StartsWith("4"); // بطاقات Visa
        }
    }

    // نظام دفع قديم - بنك الرياض
    public class RiyadBankOldSystem
    {
        public int ExecuteTransaction(float totalAmount, string cardDetails)
        {
            Console.WriteLine($"🏛️ بنك الرياض: تنفيذ معاملة بقيمة {totalAmount} ريال");
            Console.WriteLine($"   تفاصيل البطاقة: {cardDetails.Substring(0, 4)}****");
            
            // محاكاة معالجة
            System.Threading.Thread.Sleep(1200);
            
            // إرجاع كود الحالة (0 = نجح، غير ذلك = فشل)
            int statusCode = new Random().Next(0, 10) < 8 ? 0 : 1001;
            
            if (statusCode == 0)
            {
                Console.WriteLine("✅ نجحت المعاملة في بنك الرياض");
            }
            else
            {
                Console.WriteLine($"❌ فشلت المعاملة في بنك الرياض - كود الخطأ: {statusCode}");
            }
            
            return statusCode;
        }

        public string CreateTransactionReference()
        {
            return "RB" + DateTime.Now.ToString("yyyyMMddHHmmss");
        }

        public bool IsCardAccepted(string cardNumber)
        {
            Console.WriteLine($"🔍 بنك الرياض: فحص قبول البطاقة");
            
            // يقبل بطاقات MasterCard فقط
            return cardNumber.Length == 16 && cardNumber.StartsWith("5");
        }
    }

    // نظام دفع دولي قديم
    public class InternationalPaymentGateway
    {
        public PaymentResult SubmitPayment(PaymentRequest request)
        {
            Console.WriteLine($"🌍 البوابة الدولية: معالجة دفع {request.Amount} {request.CurrencyCode}");
            Console.WriteLine($"   البطاقة: {request.CardNumber.Substring(0, 4)}****");
            
            // محاكاة معالجة
            System.Threading.Thread.Sleep(1500);
            
            var result = new PaymentResult
            {
                IsSuccessful = new Random().Next(1, 11) <= 8,
                TransactionId = "INTL-" + Guid.NewGuid().ToString("N").Substring(0, 12).ToUpper(),
                ResponseMessage = ""
            };
            
            if (result.IsSuccessful)
            {
                result.ResponseMessage = "Payment processed successfully";
                Console.WriteLine("✅ نجح الدفع عبر البوابة الدولية");
            }
            else
            {
                result.ResponseMessage = "Payment failed - insufficient funds";
                Console.WriteLine("❌ فشل الدفع عبر البوابة الدولية");
            }
            
            return result;
        }

        public bool ValidateCreditCard(string cardNumber, string currencyCode)
        {
            Console.WriteLine($"🔍 البوابة الدولية: فحص البطاقة للعملة {currencyCode}");
            
            // يقبل جميع أنواع البطاقات للعملات الدولية
            return cardNumber.Length == 16;
        }
    }

    // كلاسات مساعدة للنظام الدولي
    public class PaymentRequest
    {
        public decimal Amount { get; set; }
        public string CurrencyCode { get; set; }
        public string CardNumber { get; set; }
    }

    public class PaymentResult
    {
        public bool IsSuccessful { get; set; }
        public string TransactionId { get; set; }
        public string ResponseMessage { get; set; }
    }
}
