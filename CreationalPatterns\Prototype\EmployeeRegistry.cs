using System;
using System.Collections.Generic;

namespace DesignPatterns.CreationalPatterns.Prototype
{
    /// <summary>
    /// سجل الموظفين - يحتوي على نماذج أولية (Prototypes) للموظفين
    /// هذا مفيد عندما نريد إنشاء موظفين جدد بناءً على قوالب موجودة
    /// </summary>
    public class EmployeeRegistry
    {
        private Dictionary<string, Employee> _prototypes;

        public EmployeeRegistry()
        {
            _prototypes = new Dictionary<string, Employee>();
            InitializePrototypes();
        }

        /// <summary>
        /// تهيئة النماذج الأولية الأساسية
        /// </summary>
        private void InitializePrototypes()
        {
            // نموذج مطور برمجيات
            var developerPrototype = new Employee("نموذج مطور", "تطوير البرمجيات", 8000);
            developerPrototype.AddSkill("C#");
            developerPrototype.AddSkill("ASP.NET");
            developerPrototype.AddSkill("SQL Server");
            developerPrototype.Address = new Address("شارع التقنية", "الرياض", "السعودية", "12345");
            
            // نموذج مصمم
            var designerPrototype = new Employee("نموذج مصمم", "التصميم", 6000);
            designerPrototype.AddSkill("Photoshop");
            designerPrototype.AddSkill("Illustrator");
            designerPrototype.AddSkill("UI/UX");
            designerPrototype.Address = new Address("شارع الإبداع", "جدة", "السعودية", "54321");

            // نموذج مدير مشروع
            var managerPrototype = new Employee("نموذج مدير", "إدارة المشاريع", 12000);
            managerPrototype.AddSkill("إدارة الفرق");
            managerPrototype.AddSkill("التخطيط الاستراتيجي");
            managerPrototype.AddSkill("Agile/Scrum");
            managerPrototype.Address = new Address("شارع القيادة", "الدمام", "السعودية", "67890");

            // إضافة النماذج إلى السجل
            _prototypes["Developer"] = developerPrototype;
            _prototypes["Designer"] = designerPrototype;
            _prototypes["Manager"] = managerPrototype;
        }

        /// <summary>
        /// إنشاء موظف جديد بناءً على نموذج موجود
        /// </summary>
        /// <param name="type">نوع الموظف (Developer, Designer, Manager)</param>
        /// <param name="name">اسم الموظف الجديد</param>
        /// <returns>موظف جديد منسوخ من النموذج</returns>
        public Employee CreateEmployee(string type, string name)
        {
            if (!_prototypes.ContainsKey(type))
            {
                throw new ArgumentException($"نوع الموظف '{type}' غير موجود في السجل");
            }

            // نسخ النموذج الأولي
            var newEmployee = _prototypes[type].Clone();
            
            // تخصيص الاسم الجديد
            newEmployee.Name = name;
            
            // تحديث تاريخ التوظيف
            newEmployee.HireDate = DateTime.Now;

            return newEmployee;
        }

        /// <summary>
        /// إضافة نموذج جديد إلى السجل
        /// </summary>
        public void RegisterPrototype(string type, Employee prototype)
        {
            _prototypes[type] = prototype;
        }

        /// <summary>
        /// الحصول على قائمة بأنواع الموظفين المتاحة
        /// </summary>
        public IEnumerable<string> GetAvailableTypes()
        {
            return _prototypes.Keys;
        }
    }
}
