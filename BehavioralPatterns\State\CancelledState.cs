using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// حالة "ملغي"
    /// الطلب تم إلغاؤه
    /// </summary>
    public class CancelledState : IOrderState
    {
        public void ConfirmOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن تأكيد طلب ملغي");
            Console.WriteLine("💡 يمكنك إنشاء طلب جديد");
        }

        public void ShipOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن شحن طلب ملغي");
        }

        public void DeliverOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن تسليم طلب ملغي");
        }

        public void CancelOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب ملغي بالفعل");
        }

        public void ReturnOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن إرجاع طلب ملغي");
        }

        public string GetStateName()
        {
            return "ملغي";
        }

        public string[] GetAvailableActions()
        {
            return new string[0]; // لا توجد عمليات متاحة
        }
    }
}
