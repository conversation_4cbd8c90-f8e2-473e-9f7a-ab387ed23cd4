using System;

namespace DesignPatterns.CreationalPatterns.Prototype
{
    /// <summary>
    /// عرض توضيحي لاستخدام Prototype Pattern
    /// </summary>
    public class PrototypeDemo
    {
        public static void RunDemo()
        {
            Console.WriteLine("=== Prototype Pattern Demo ===\n");

            // إنشاء سجل الموظفين
            var employeeRegistry = new EmployeeRegistry();

            Console.WriteLine("الأنواع المتاحة من الموظفين:");
            foreach (var type in employeeRegistry.GetAvailableTypes())
            {
                Console.WriteLine($"- {type}");
            }
            Console.WriteLine();

            // إنشاء موظفين جدد بناءً على النماذج الأولية
            try
            {
                // إنشاء مطور جديد
                var developer1 = employeeRegistry.CreateEmployee("Developer", "أحمد محمد");
                developer1.Salary = 9000; // تعديل الراتب
                developer1.AddSkill("React"); // إضافة مهارة جديدة

                var developer2 = employeeRegistry.CreateEmployee("Developer", "فاطمة علي");
                developer2.Salary = 8500;
                developer2.AddSkill("Angular");

                // إنشاء مصمم جديد
                var designer1 = employeeRegistry.CreateEmployee("Designer", "سارة أحمد");
                designer1.Salary = 6500;
                designer1.Address.City = "الرياض"; // تغيير المدينة

                Console.WriteLine("الموظفون الجدد المنشأون من النماذج الأولية:\n");
                
                Console.WriteLine("المطور الأول:");
                Console.WriteLine(developer1);
                Console.WriteLine("---");

                Console.WriteLine("المطور الثاني:");
                Console.WriteLine(developer2);
                Console.WriteLine("---");

                Console.WriteLine("المصممة:");
                Console.WriteLine(designer1);
                Console.WriteLine("---");

                // إثبات أن النسخ عميق وليس سطحي
                Console.WriteLine("=== اختبار النسخ العميق ===");
                
                var originalEmployee = employeeRegistry.CreateEmployee("Developer", "الموظف الأصلي");
                var clonedEmployee = originalEmployee.Clone();
                
                // تعديل المهارات في النسخة المستنسخة
                clonedEmployee.Name = "الموظف المستنسخ";
                clonedEmployee.AddSkill("Python");
                clonedEmployee.Address.City = "مكة";

                Console.WriteLine("الموظف الأصلي:");
                Console.WriteLine($"الاسم: {originalEmployee.Name}");
                Console.WriteLine($"المهارات: {string.Join(", ", originalEmployee.Skills)}");
                Console.WriteLine($"المدينة: {originalEmployee.Address.City}");
                Console.WriteLine();

                Console.WriteLine("الموظف المستنسخ:");
                Console.WriteLine($"الاسم: {clonedEmployee.Name}");
                Console.WriteLine($"المهارات: {string.Join(", ", clonedEmployee.Skills)}");
                Console.WriteLine($"المدينة: {clonedEmployee.Address.City}");
                Console.WriteLine();

                Console.WriteLine("✅ النسخ العميق يعمل بشكل صحيح - تغيير النسخة لا يؤثر على الأصل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ: {ex.Message}");
            }
        }
    }
}
