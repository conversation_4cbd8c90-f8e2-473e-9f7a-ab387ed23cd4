using System;

namespace DesignPatterns.BehavioralPatterns.State
{
    /// <summary>
    /// حالة "تم الإرجاع"
    /// الطلب تم إرجاعه من العميل
    /// </summary>
    public class ReturnedState : IOrderState
    {
        public void ConfirmOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن تأكيد طلب تم إرجاعه");
        }

        public void ShipOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن شحن طلب تم إرجاعه");
        }

        public void DeliverOrder(OrderContext context)
        {
            Console.WriteLine("❌ لا يمكن تسليم طلب تم إرجاعه");
        }

        public void CancelOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب في حالة الإرجاع (مشابه للإلغاء)");
        }

        public void ReturnOrder(OrderContext context)
        {
            Console.WriteLine("ℹ️ الطلب في عملية الإرجاع بالفعل");
            Console.WriteLine("📞 يمكنك التواصل مع خدمة العملاء لمتابعة حالة الإرجاع");
        }

        public string GetStateName()
        {
            return "تم الإرجاع";
        }

        public string[] GetAvailableActions()
        {
            return new string[0]; // لا توجد عمليات متاحة (أو يمكن إضافة "متابعة الإرجاع")
        }
    }
}
