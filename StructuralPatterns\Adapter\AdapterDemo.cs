using System;

namespace DesignPatterns.StructuralPatterns.Adapter
{
    /// <summary>
    /// عرض توضيحي لاستخدام Adapter Pattern
    /// </summary>
    public class AdapterDemo
    {
        public static void RunDemo()
        {
            Console.WriteLine("=== Adapter Pattern Demo ===\n");

            // إنشاء الأنظمة القديمة (Adaptees)
            var alAhliLegacySystem = new AlAhliLegacyPaymentSystem();
            var riyadBankOldSystem = new RiyadBankOldSystem();
            var internationalGateway = new InternationalPaymentGateway();

            // إنشاء المحولات (Adapters)
            var alAhliAdapter = new AlAhliPaymentAdapter(alAhliLegacySystem);
            var riyadBankAdapter = new RiyadBankAdapter(riyadBankOldSystem);
            var internationalAdapter = new InternationalPaymentAdapter(internationalGateway);

            // إنشاء خدمة الدفع الموحدة
            var paymentService = new PaymentService();

            // إضافة جميع المحولات
            Console.WriteLine("🔧 إعداد معالجات الدفع:");
            Console.WriteLine(new string('=', 40));
            paymentService.AddPaymentProcessor(alAhliAdapter);
            paymentService.AddPaymentProcessor(riyadBankAdapter);
            paymentService.AddPaymentProcessor(internationalAdapter);

            // عرض المعالجات المتاحة
            paymentService.ShowAvailableProcessors();

            // === اختبار أنواع مختلفة من البطاقات ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("🧪 اختبار أنواع مختلفة من البطاقات");
            Console.WriteLine(new string('*', 70));

            // بطاقة Visa (يدعمها البنك الأهلي والبوابة الدولية)
            string visaCard = "****************";
            Console.WriteLine($"\n💳 اختبار بطاقة Visa: {visaCard.Substring(0, 4)}****");
            paymentService.TestAllProcessors(visaCard);

            // بطاقة MasterCard (يدعمها بنك الرياض والبوابة الدولية)
            string masterCard = "****************";
            Console.WriteLine($"\n💳 اختبار بطاقة MasterCard: {masterCard.Substring(0, 4)}****");
            paymentService.TestAllProcessors(masterCard);

            // === سيناريوهات الدفع ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("💰 سيناريوهات الدفع");
            Console.WriteLine(new string('*', 70));

            // سيناريو 1: دفع بالريال السعودي
            Console.WriteLine("\n1️⃣ سيناريو: دفع بالريال السعودي");
            paymentService.ProcessPayment(150.00m, "SAR", visaCard);

            // سيناريو 2: دفع بالدولار الأمريكي
            Console.WriteLine("\n2️⃣ سيناريو: دفع بالدولار الأمريكي");
            paymentService.ProcessPayment(40.00m, "USD", masterCard);

            // سيناريو 3: دفع باليورو
            Console.WriteLine("\n3️⃣ سيناريو: دفع باليورو");
            paymentService.ProcessPayment(35.00m, "EUR", visaCard);

            // === إظهار فوائد Adapter Pattern ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("📊 فوائد Adapter Pattern");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("\n✅ بدون Adapter Pattern كان سيتطلب:");
            Console.WriteLine("   • كتابة كود منفصل لكل نظام دفع");
            Console.WriteLine("   • تذكر واجهة كل نظام");
            Console.WriteLine("   • تعديل الكود عند إضافة نظام جديد");
            Console.WriteLine("   • تعقيد في إدارة أنواع البيانات المختلفة");

            Console.WriteLine("\n🎯 مع Adapter Pattern نحصل على:");
            Console.WriteLine("   • واجهة موحدة لجميع أنظمة الدفع");
            Console.WriteLine("   • سهولة إضافة أنظمة دفع جديدة");
            Console.WriteLine("   • إعادة استخدام الأنظمة القديمة");
            Console.WriteLine("   • فصل منطق التحويل عن منطق العمل");

            // === مثال على الكود بدون Adapter ===
            Console.WriteLine("\n🔍 مثال: الكود بدون Adapter Pattern");
            Console.WriteLine("```csharp");
            Console.WriteLine("// بدون Adapter - كود معقد ومتكرر");
            Console.WriteLine("if (paymentMethod == \"AlAhli\") {");
            Console.WriteLine("    var alAhli = new AlAhliLegacyPaymentSystem();");
            Console.WriteLine("    double amountSAR = ConvertToSAR(amount, currency);");
            Console.WriteLine("    bool success = alAhli.AuthorizePayment(amountSAR, cardNumber);");
            Console.WriteLine("    if (success) transactionId = alAhli.GenerateTransactionId();");
            Console.WriteLine("} else if (paymentMethod == \"Riyad\") {");
            Console.WriteLine("    var riyad = new RiyadBankOldSystem();");
            Console.WriteLine("    float amountFloat = (float)ConvertToSAR(amount, currency);");
            Console.WriteLine("    int status = riyad.ExecuteTransaction(amountFloat, cardNumber);");
            Console.WriteLine("    if (status == 0) transactionId = riyad.CreateTransactionReference();");
            Console.WriteLine("} // ... وهكذا لكل نظام");
            Console.WriteLine("```");

            Console.WriteLine("\n✨ مع Adapter Pattern:");
            Console.WriteLine("```csharp");
            Console.WriteLine("// مع Adapter - كود بسيط وموحد");
            Console.WriteLine("string transactionId = paymentProcessor.ProcessPayment(amount, currency, cardNumber);");
            Console.WriteLine("```");

            Console.WriteLine("\n🏆 Adapter Pattern يحقق:");
            Console.WriteLine("• إعادة الاستخدام للكود الموجود");
            Console.WriteLine("• التوافق بين الواجهات المختلفة");
            Console.WriteLine("• المرونة في إضافة أنظمة جديدة");
            Console.WriteLine("• تبسيط الكود وتحسين قابليته للصيانة");
        }
    }
}
