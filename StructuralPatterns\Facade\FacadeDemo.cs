using System;
using System.Threading;

namespace DesignPatterns.StructuralPatterns.Facade
{
    /// <summary>
    /// عرض توضيحي لاستخدام Facade Pattern
    /// </summary>
    public class FacadeDemo
    {
        public static void RunDemo()
        {
            Console.WriteLine("=== Facade Pattern Demo ===\n");

            // إنشاء واجهة المسرح المنزلي
            var homeTheater = new HomeTheaterFacade();

            // عرض حالة النظام
            homeTheater.ShowSystemStatus();

            // === سيناريو 1: مشاهدة فيلم ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("🎬 سيناريو 1: مشاهدة فيلم");
            Console.WriteLine(new string('*', 70));

            homeTheater.WatchMovie("The Matrix");
            
            // محاكاة مشاهدة الفيلم
            Console.WriteLine("\n⏳ جاري مشاهدة الفيلم...");
            Thread.Sleep(2000);
            
            homeTheater.EndMovie();

            // === سيناريو 2: الاستماع للموسيقى ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("🎵 سيناريو 2: الاستماع للموسيقى");
            Console.WriteLine(new string('*', 70));

            homeTheater.ListenToMusic("قائمة الموسيقى الكلاسيكية");
            
            Thread.Sleep(1500);
            Console.WriteLine("🎼 جاري الاستماع للموسيقى...");
            Thread.Sleep(1000);

            // === سيناريو 3: اللعب ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("🎮 سيناريو 3: اللعب");
            Console.WriteLine(new string('*', 70));

            homeTheater.PlayGame("FIFA 2024");
            
            Thread.Sleep(1500);
            Console.WriteLine("🏆 جاري اللعب...");
            Thread.Sleep(1000);

            // === إيقاف جميع الأنظمة ===
            homeTheater.ShutdownAll();

            // === مقارنة مع وبدون Facade ===
            Console.WriteLine("\n" + new string('*', 70));
            Console.WriteLine("📊 مقارنة: مع وبدون Facade Pattern");
            Console.WriteLine(new string('*', 70));

            Console.WriteLine("\n❌ بدون Facade Pattern - العميل يحتاج لـ:");
            Console.WriteLine("   1. معرفة جميع الأنظمة الفرعية");
            Console.WriteLine("   2. تذكر تسلسل العمليات المعقد");
            Console.WriteLine("   3. التعامل مع كل نظام بشكل منفصل");
            Console.WriteLine("   4. إدارة الأخطاء لكل نظام");

            Console.WriteLine("\n✅ مع Facade Pattern - العميل يحتاج لـ:");
            Console.WriteLine("   1. استدعاء دالة واحدة بسيطة");
            Console.WriteLine("   2. تمرير المعاملات الأساسية فقط");
            Console.WriteLine("   3. الحصول على النتيجة المطلوبة");

            // === مثال على التعقيد بدون Facade ===
            Console.WriteLine("\n🔍 مثال: تشغيل فيلم بدون Facade:");
            Console.WriteLine("```csharp");
            Console.WriteLine("// بدون Facade - كود معقد ومتكرر");
            Console.WriteLine("var curtains = new CurtainSystem();");
            Console.WriteLine("var lights = new LightingSystem();");
            Console.WriteLine("var climate = new ClimateControl();");
            Console.WriteLine("var projector = new ProjectorSystem();");
            Console.WriteLine("var audio = new AudioSystem();");
            Console.WriteLine("var dvd = new DVDPlayer();");
            Console.WriteLine("");
            Console.WriteLine("curtains.Close();");
            Console.WriteLine("lights.SetMovieMode();");
            Console.WriteLine("climate.SetMovieMode();");
            Console.WriteLine("projector.TurnOn();");
            Console.WriteLine("projector.SetInput(\"DVD\");");
            Console.WriteLine("projector.SetResolution(\"4K\");");
            Console.WriteLine("audio.TurnOn();");
            Console.WriteLine("audio.SetSurroundSound();");
            Console.WriteLine("audio.SetVolume(75);");
            Console.WriteLine("dvd.TurnOn();");
            Console.WriteLine("dvd.InsertDisc(\"The Matrix\");");
            Console.WriteLine("dvd.Play();");
            Console.WriteLine("```");

            Console.WriteLine("\n✨ مع Facade - كود بسيط:");
            Console.WriteLine("```csharp");
            Console.WriteLine("var homeTheater = new HomeTheaterFacade();");
            Console.WriteLine("homeTheater.WatchMovie(\"The Matrix\");");
            Console.WriteLine("```");

            Console.WriteLine("\n🎯 فوائد Facade Pattern:");
            Console.WriteLine("• تبسيط الواجهة المعقدة");
            Console.WriteLine("• إخفاء تفاصيل التنفيذ");
            Console.WriteLine("• تقليل الاقتران بين العميل والأنظمة الفرعية");
            Console.WriteLine("• سهولة الاستخدام والصيانة");
            Console.WriteLine("• إمكانية تغيير الأنظمة الداخلية دون تأثير على العميل");
        }
    }
}
